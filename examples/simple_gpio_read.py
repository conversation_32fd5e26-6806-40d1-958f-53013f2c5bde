#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GPIO读取测试
使用wiringPi库，wiringPi引脚编号27
"""

import wiringpi
import time

# 初始化wiringPi
wiringpi.wiringPiSetup()

# 设置引脚27为输入模式
GPIO_PIN = 27
wiringpi.pinMode(GPIO_PIN, wiringpi.INPUT)
wiringpi.pullUpDnControl(GPIO_PIN, wiringpi.PUD_DOWN)  # 启用下拉电阻

print(f"GPIO引脚 {GPIO_PIN} 读取测试开始...")

try:
    while True:
        # 读取引脚电平
        level = wiringpi.digitalRead(GPIO_PIN)

        # 显示结果
        if level == 1:
            print("引脚27: 高电平 (1)")
        else:
            print("引脚27: 低电平 (0)")

        time.sleep(0.5)  # 每0.5秒读取一次

except KeyboardInterrupt:
    print("\n测试结束")
