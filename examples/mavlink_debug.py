#!/usr/bin/env python3
"""
调试MAVLink连接问题
"""


import time
import logging
from lib.hardware.mavlink_handler import MavlinkHandler

# 配置日志
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def debug_connection_cycle():
    """调试连接循环"""
    print("=" * 60)
    print("调试MAVLink连接循环")
    print("=" * 60)

    handler = MavlinkHandler()

    for cycle in range(3):
        print(f"\n--- 循环 {cycle + 1} ---")

        # 尝试连接
        print("1. 尝试连接...")
        success = handler.connect()
        print(f"连接结果: {success}")

        if success:
            status = handler.get_connection_status()
            print(f"连接状态: {status}")

            # 模拟消息更新循环
            print("2. 模拟消息更新...")
            for i in range(5):
                print(f"  更新 {i+1}/5")
                has_messages = handler.update_messages()
                current_status = handler.get_connection_status()
                print(f"    有消息: {has_messages}")
                print(
                    f"    连接状态: connected={current_status['connected']}, "
                    f"lost={current_status['connection_lost']}"
                )
                print(f"    时间差: {current_status['time_since_last_update']}ms")

                if current_status["connection_lost"]:
                    print("    检测到连接丢失!")
                    break

                time.sleep(1)

        print("3. 等待5秒后下一轮...")
        time.sleep(5)


def main():
    try:
        debug_connection_cycle()
    except KeyboardInterrupt:
        print("\n调试被用户中断")
    except Exception as e:
        print(f"\n调试过程中出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
