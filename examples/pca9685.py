from smbus2 import SMBus
import time


class PCA9685:
    def __init__(self, bus=3, address=0x40):
        self.bus = SMBus(bus)
        self.address = address
        self.set_pwm_freq(50)  # 50Hz = 20ms 周期，适合伺服或普通 PWM 控制

    def write(self, reg, value):
        self.bus.write_byte_data(self.address, reg, value)

    def set_pwm_freq(self, freq_hz):
        prescale = int(25000000.0 / (4096 * freq_hz) - 1)
        old_mode = self.bus.read_byte_data(self.address, 0x00)
        self.write(0x00, (old_mode & 0x7F) | 0x10)  # Sleep
        self.write(0xFE, prescale)  # Set prescaler
        self.write(0x00, old_mode)  # Wake up
        time.sleep(0.005)
        self.write(0x00, old_mode | 0x80)  # Restart

    def set_pwm(self, channel, on, off):
        reg = 0x06 + 4 * channel
        self.write(reg, on & 0xFF)
        self.write(reg + 1, on >> 8)
        self.write(reg + 2, off & 0xFF)
        self.write(reg + 3, off >> 8)


# === 实例化 PCA9685，使用 i2c-3 端口 ===
pwm = PCA9685(bus=3)

# === 控制通道 15 ===
channel = 15
duty = 0
direction = 1

try:
    while True:
        pulse = int(4095 * duty / 100)
        pwm.set_pwm(channel, 0, pulse)
        print(f"通道 {channel}：占空比 {duty}% -> PWM {pulse}/4095")
        duty += direction
        if duty >= 100:
            duty = 100
            direction = -1
        elif duty <= 0:
            duty = 0
            direction = 1
        time.sleep(0.02)
except KeyboardInterrupt:
    print("退出程序，关闭通道 PWM")
    pwm.set_pwm(channel, 0, 0)
