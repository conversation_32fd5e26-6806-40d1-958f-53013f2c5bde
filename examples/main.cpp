#include <Arduino.h>
#include <HardwareSerial.h>
#include "main.h"
#include "web.h"
#include "basic.h"

// 系统配置和状态变量
AsyncWebServer server(80);
MotorCANControl motor(CAN_TX, CAN_RX, 0x01);
WinchConfig winchConfig;
ControlMode currentMode = REMOTE_CONTROL;
bool emergencyStopActive = false;

// 串口通信相关
HardwareSerial sbusSerial(1);
HardwareSerial SerialPrint(2);
bfs::SbusRx sbus_rx(&sbusSerial, SBUS_RX_PIN, SBUS_TX_PIN, true);

// PWM输出相关定义
const int PWM1_PIN = 33;          // 第一个PWM输出引脚
const int PWM2_PIN = 34;          // 第二个PWM输出引脚
const int OUTPUT_FREQ = 1000;     // 输出PWM频率（Hz）
const int OUTPUT_RESOLUTION = 12; // 输出PWM分辨率（位）
const float PWM_DUTY = 0.45;      // 占空比

// PWM输出值
int pwm1_value = 0;
int pwm2_value = 0;

// 任务句柄
TaskHandle_t motorCommunicationTask;
TaskHandle_t printTask;
TaskHandle_t networkMonitorTask;

// 电机控制相关变量
float targetSpeed = 0.0;
MotorData latestMotorData;

// SBUS和校准相关变量
bool isCalibrating = false;
bool sbusSignalDetected = false;
unsigned long lastSbusUpdateTime = 0;

const unsigned long SBUS_DEBOUNCE_TIME = 500; // SBUS状态变化防抖时间
unsigned long lastSbusStateChange = 0;        // 上次SBUS状态变化时间
bool lastSbusState = false;                   // 上次SBUS状态

// 通道校准数据
ChannelCalibration channelCal[16] = {
    {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}, {0xFFFF, 0, 0}};

// 绞盘物理参数（单位：米）
float innerDiameter = 0.044f;    // 绞盘内径
float cableDiameter = 0.0018f;   // 缆绳直径
float cylinderLength = 0.0525f;  // 盘缆筒长度
float totalCableLength = 100.0f; // 总缆绳长度

// 控制参数
bool lastEnable = false;                         // 上一次的使能状态
float lastRemoteSpeed = 0.0f;                    // 上一次的遥控器设置速度
unsigned long lastEnableToggleTime = 0;          // 上次使能状态变化的时间
const unsigned long ENABLE_DEBOUNCE_TIME = 1000; // 使能切换防抖时间(毫秒)
const float CENTER_DEADBAND = 0.15f;             // 中位死区大小（归一化值的范围）
const int DEAD_ZONE = 20;                        // 死区范围

// 处理遥控器控制逻辑
void handleRemoteControl()
{
    if (!sbusSignalDetected)
    {
        Serial.println("警告: SBUS信号丢失!");
        motor.setVelocityMode(0.0);
        // motor.setVelocityMode(speed);
        latestMotorData.isEnabled = false;
        lastEnable = false;
        lastRemoteSpeed = 0.0f;
        return;
    }

    sbus_data = sbus_rx.data();

    // 获取通道索引
    int enableChIndex = winchConfig.enableChannel - 1;
    int controlChIndex = winchConfig.controlChannel - 1;

    // 获取校准范围
    uint16_t enableMinVal = channelCal[enableChIndex].min != 0xFFFF ? channelCal[enableChIndex].min : 372;
    uint16_t enableMaxVal = channelCal[enableChIndex].max != 0 ? channelCal[enableChIndex].max : 1630;
    uint16_t controlMinVal = channelCal[controlChIndex].min != 0xFFFF ? channelCal[controlChIndex].min : 372;
    uint16_t controlMaxVal = channelCal[controlChIndex].max != 0 ? channelCal[controlChIndex].max : 1630;
    uint64_t controlMidValue = (controlMaxVal - controlMinVal) / 2 + controlMinVal;

    // 处理使能通道
    uint16_t enableRaw = valueClamp(sbus_data.ch[enableChIndex], enableMinVal, enableMaxVal);
    bool newEnable = (enableRaw > 1500);

    // 处理使能状态变化
    unsigned long currentTime = millis();
    if (newEnable != lastEnable && (currentTime - lastEnableToggleTime) > ENABLE_DEBOUNCE_TIME)
    {
        lastEnable = newEnable;
        lastEnableToggleTime = currentTime;
        // Serial.println(newEnable ? "遥控器：电机使能" : "遥控器：电机失能");
    }

    // 处理控制通道
    uint16_t pwmChValue = valueClamp(sbus_data.ch[controlChIndex], controlMinVal, controlMaxVal);
    float maxSpeedLimit = (latestMotorData.totalRounds <= LIMIT_ROUNDS) ? INITIAL_MAX_SPEED : winchConfig.maxSpeed;
    float speed = 0.0f;

    // 计算速度，考虑急停状态
    if (!emergencyStopActive && (pwmChValue >= (controlMidValue + DEAD_ZONE) || pwmChValue <= (controlMidValue - DEAD_ZONE)))
    {
        speed = (float)map(pwmChValue, controlMinVal, controlMaxVal, -maxSpeedLimit, maxSpeedLimit);
    }
    else if (emergencyStopActive && pwmChValue <= (controlMidValue - DEAD_ZONE))
    {
        speed = (float)map(pwmChValue, controlMinVal, controlMaxVal, -maxSpeedLimit, maxSpeedLimit);
    }

    // 更新电机状态
    if (lastEnable)
    {
        // motor.Motor_Enable();
        motor.setVelocityMode(speed);
        latestMotorData.isEnabled = true;
    }
    else
    {
        motor.setVelocityMode(0.0);
        latestMotorData.isEnabled = false;
    }
}

// 处理PC控制逻辑
void handlePCControl()
{
    if (!latestMotorData.isEnabled)
    {
        motor.setVelocityMode(0.0);
        return;
    }

    float finalSpeed = 0.0f;

    if (targetSpeed != 0.0)
    {
        if (!emergencyStopActive || targetSpeed < 0)
        {
            finalSpeed = -targetSpeed;
        }
    }

    motor.setVelocityMode(finalSpeed);
}

void motorCommunicationTaskFunction(void *parameter)
{
    TickType_t xLastWakeTime;
    const TickType_t xFrequency = pdMS_TO_TICKS(20); // 提高更新频率到20ms
    xLastWakeTime = xTaskGetTickCount();
    bool lastEmergencyState = false;
    double lastTotalRounds = 0;         // 记录上次的圈数
    const double SPEED_THRESHOLD = 0.1; // 速度阈值，小于此值认为电机停止

    for (;;)
    {
        vTaskDelayUntil(&xLastWakeTime, xFrequency);

        // 处理急停状态变化
        if (emergencyStopActive != lastEmergencyState)
        {
            Serial.println(emergencyStopActive ? "⚠️ 急停激活！仅允许放缆操作" : "急停解除，恢复正常控制");
            lastEmergencyState = emergencyStopActive;
        }

        // 根据当前模式处理控制逻辑
        if (currentMode == REMOTE_CONTROL)
        {
            handleRemoteControl();
        }
        else
        {
            handlePCControl();
        }

        // 更新电机数据
        // 接收并更新电机数据
        if (motor.updateStatus())
        {
            const MotorCANControl::MotorStatus &status = motor.getStatus();

            // 检查错误状态
            if (status.error_state != MotorCANControl::ErrorState::MOTOR_ENABLED)
            {
                Serial.printf("电机错误: %s\n", status.error_description);
                // 如果发生错误，尝试清除错误
                if (status.error_state != MotorCANControl::ErrorState::MOTOR_DISABLED)
                {
                    motor.clearError();
                }
                else
                    motor.enable();
            }

            // 更新全局变量
            double Distance = calculateCurrentCableLength(-status.total_rounds, innerDiameter, cableDiameter, cylinderLength, totalCableLength);
            latestMotorData.totalLength = Distance;
            latestMotorData.angle = Distance; // 需要替换成缆绳的长度
            latestMotorData.speed = -status.velocity;
            latestMotorData.torque = -status.torque;
            latestMotorData.temperature = status.temp_rotor;
            latestMotorData.totalRounds = -status.total_rounds; // 取反，确保放线为正
        }
    }
}

void printTaskFunction(void *pvParameters)
{
    while (1)
    {
        // 暂时注释掉所有打印，等待实际需要时再启用
        printMotorInfo(latestMotorData);
        vTaskDelay(pdMS_TO_TICKS(1000)); // 1s 打印一次
    }
}

// 网络监控任务
void networkMonitorTaskFunction(void *pvParameters)
{
    bool lastEthConnected = ethConnected;
    unsigned long lastReconnectAttempt = 0;
    const unsigned long RECONNECT_INTERVAL = 5000;
    bool isReconnecting = false;

    while (1)
    {
        bool currentEthStatus = ETH.linkUp(); // 检查物理连接状态

        if (currentEthStatus != lastEthConnected)
        {
            if (currentEthStatus)
            {
                // 网络刚刚恢复
                ethConnected = true;
                Serial.println("✅ 有线网络已恢复连接!");
                Serial.printf("IP地址: %s\n", ETH.localIP().toString().c_str());
            }
            else
            {
                // 网络刚刚断开
                ethConnected = false;
                currentMode = REMOTE_CONTROL;
                Serial.println("⚠️ 有线网络断开，切换到遥控器控制模式");
            }
            lastEthConnected = currentEthStatus;
        }

        // 如果网络断开，尝试重连
        if (!ethConnected && !isReconnecting)
        {
            if (millis() - lastReconnectAttempt >= RECONNECT_INTERVAL)
            {
                isReconnecting = true;
                lastReconnectAttempt = millis();

                if (ETH.begin())
                {
                    ethConnected = true;
                }
                else
                {
                    ethConnected = false;
                }
            }
        }

        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

void setup()
{
    Serial.begin(115200);

    SerialPrint.begin(115200, SERIAL_8N1, SerialPrint_RX, SerialPrint_TX);

    // 关闭调试输出，避免干扰
    Serial.setDebugOutput(false);

    pinMode(EMERGENCY_STOP_PIN, INPUT_PULLDOWN);

    // 初始化PWM输出
    ledcSetup(0, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道0，频率1kHz，12位分辨率
    ledcSetup(1, OUTPUT_FREQ, OUTPUT_RESOLUTION); // 通道1，频率1kHz，12位分辨率

    // 确保引脚正确配置
    pinMode(PWM1_PIN, OUTPUT);
    pinMode(PWM2_PIN, OUTPUT);

    ledcAttachPin(PWM1_PIN, 0); // 将GPIO33连接到通道0
    ledcAttachPin(PWM2_PIN, 1); // 将GPIO34连接到通道1

    // 初始化SPIFFS文件系统
    if (!SPIFFS.begin(true))
    {
        Serial.println("SPIFFS挂载失败");
        return;
    }

    // 初始化EEPROM并加载配置
    EEPROM.begin(EEPROM_SIZE + EEPROM_CONFIG_SIZE);

    // 加载校准数据和绞车配置
    loadCalibrationData();
    loadWinchConfig();

    // 初始化网络 - 确保在Web服务器前初始化
    WiFi.mode(WIFI_OFF);
    vTaskDelay(1000);

    Serial.println("开始初始化有线网络...");
    setupEthernet();

    // 等待有线网络连接，增加超时时间
    int attempts = 0;
    const int MAX_ATTEMPTS = 10; // 增加最大尝试次数
    while (!ethConnected && attempts < MAX_ATTEMPTS)
    {
        delay(500);
        Serial.print(".");
        attempts++;
    }

    if (ethConnected)
    {
        Serial.println("\n有线网络连接成功!");
        Serial.printf("IP地址: %s\n", ETH.localIP().toString().c_str());
        vTaskDelay(1000); // 等待网络稳定
    }
    else
    {
        Serial.println("\n等待LAN连接,系统将以遥控器模式启动");
        currentMode = REMOTE_CONTROL; // 如果网络连接失败，默认使用遥控器模式
        vTaskDelay(1000);
    }

    vTaskDelay(1000);

    // 初始化SBUS接收机
    sbus_rx.Begin();

    // 初始化CAN和电机
    initMotor();

    // 先确保TCP/IP栈已初始化，再设置Web服务器
    setupWebServer();

    vTaskDelay(1000);

    xTaskCreatePinnedToCore(
        motorCommunicationTaskFunction,
        "MotorCommunicationTask",
        16384,
        NULL,
        1,
        &motorCommunicationTask,
        0);

    xTaskCreatePinnedToCore(
        printTaskFunction,
        "printTask",
        4096,
        NULL,
        2,
        &printTask,
        1);

    xTaskCreatePinnedToCore(
        networkMonitorTaskFunction,
        "NetworkMonitorTask",
        16384,
        NULL,
        1,
        &networkMonitorTask,
        1);
}

void loop()
{
    emergencyStopActive = digitalRead(EMERGENCY_STOP_PIN) == HIGH;

    // 改进的SBUS信号检测逻辑
    bool currentSbusState = sbus_rx.Read();
    unsigned long currentTime = millis();

    if (currentSbusState)
    {
        lastSbusUpdateTime = currentTime;

        // 处理PWM输出
        int pwm1_ch = map(valueClamp(sbus_data.ch[12], 372, 1630), 372, 1630, 1000, 2000);
        int pwm2_ch = map(valueClamp(sbus_data.ch[13], 372, 1630), 372, 1630, 1000, 2000);

        // 应用死区
        if (pwm1_ch < 1500 + 20)
            pwm1_ch = 1500;
        if (pwm2_ch < 1500 + 20)
            pwm2_ch = 1500;

        // 将通道值映射到PWM输出范围（修改映射方式）
        pwm1_value = map(pwm1_ch, 1000, 2000, 0, 4095) * PWM_DUTY;
        pwm2_value = map(pwm2_ch, 1000, 2000, 0, 4095) * PWM_DUTY;

        // 输出PWM信号
        ledcWrite(0, pwm1_value);
        ledcWrite(1, pwm2_value);

        // 只有当状态真正改变时才更新
        if (!sbusSignalDetected && (currentTime - lastSbusStateChange) > SBUS_DEBOUNCE_TIME)
        {
            sbusSignalDetected = true;
            lastSbusStateChange = currentTime;
            Serial.println("✅ SBUS信号已连接");
        }
    }
    else if (currentTime - lastSbusUpdateTime > SBUS_TIMEOUT)
    {

        // 只有当状态真正改变时才更新
        if (sbusSignalDetected && (currentTime - lastSbusStateChange) > SBUS_DEBOUNCE_TIME)
        {
            sbusSignalDetected = false;
            lastSbusStateChange = currentTime;
            Serial.println("⚠️ SBUS信号丢失!");
        }
    }

    // 短暂延时避免CPU高占用
    vTaskDelay(50);
}