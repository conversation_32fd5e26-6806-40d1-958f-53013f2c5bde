#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO高低电平读取测试
使用wiringPi库，wiringPi引脚编号系统中的27号引脚
"""

import wiringpi
import time

# wiringPi引脚编号
GPIO_PIN = 27


def setup_gpio():
    """初始化GPIO设置"""
    # 初始化wiringPi库
    if wiringpi.wiringPiSetup() == -1:
        print("错误：无法初始化wiringPi库")
        return False

    # 设置引脚为输入模式
    wiringpi.pinMode(GPIO_PIN, wiringpi.INPUT)

    # 启用内部上拉电阻（可选）
    wiringpi.pullUpDnControl(GPIO_PIN, wiringpi.PUD_UP)

    print(f"GPIO引脚 {GPIO_PIN} 已设置为输入模式（带上拉电阻）")
    return True


def read_gpio_level():
    """读取GPIO引脚的电平状态"""
    level = wiringpi.digitalRead(GPIO_PIN)
    return level


def continuous_read_test():
    """连续读取GPIO电平测试"""
    print(f"开始连续读取GPIO引脚 {GPIO_PIN} 的电平状态...")
    print("按 Ctrl+C 停止测试")

    try:
        last_level = None
        while True:
            current_level = read_gpio_level()

            # 只在电平发生变化时打印
            if current_level != last_level:
                level_str = "高电平 (HIGH)" if current_level == 1 else "低电平 (LOW)"
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] GPIO {GPIO_PIN}: {level_str} ({current_level})")
                last_level = current_level

            time.sleep(0.1)  # 100ms间隔

    except KeyboardInterrupt:
        print("\n测试已停止")


def single_read_test():
    """单次读取GPIO电平测试"""
    print(f"单次读取GPIO引脚 {GPIO_PIN} 的电平状态:")

    for i in range(5):
        level = read_gpio_level()
        level_str = "高电平 (HIGH)" if level == 1 else "低电平 (LOW)"
        print(f"读取 {i+1}: {level_str} ({level})")
        time.sleep(1)


def main():
    """主函数"""
    print("=== GPIO高低电平读取测试 ===")
    print(f"使用wiringPi引脚编号: {GPIO_PIN}")
    print()

    # 初始化GPIO
    if not setup_gpio():
        return

    print("选择测试模式:")
    print("1. 单次读取测试（读取5次）")
    print("2. 连续读取测试（实时监控）")

    try:
        choice = input("请输入选择 (1 或 2): ").strip()

        if choice == "1":
            single_read_test()
        elif choice == "2":
            continuous_read_test()
        else:
            print("无效选择，默认执行单次读取测试")
            single_read_test()

    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
