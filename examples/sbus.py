from pymavlink import mavutil
import time
import sys


def try_connect_udp():
    """尝试连接UDP端口"""
    try:
        print("尝试连接UDP: 127.0.0.1:14555...")
        master = mavutil.mavlink_connection("udp:127.0.0.1:14555")

        # 设置较短的超时时间来快速检测连接
        master.mav.srcSystem = 255
        master.mav.srcComponent = 0

        # 等待心跳，设置5秒超时
        print("等待UDP心跳...")
        start_time = time.time()
        while time.time() - start_time < 5:
            master.wait_heartbeat(timeout=1)
            if master.target_system != 0:
                print(f"UDP连接成功，系统ID：{master.target_system}")
                return master

        print("UDP连接超时")
        return None

    except Exception as e:
        print(f"UDP连接失败: {e}")
        return None


def check_mavlink_routerd():
    """检查mavlink-routerd是否在运行"""
    import subprocess

    try:
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        return "mavlink-routerd" in result.stdout
    except:
        return False


def try_connect_serial():
    """尝试连接串口"""
    # 首先检查mavlink-routerd是否在运行
    if check_mavlink_routerd():
        print("检测到mavlink-routerd正在运行，它可能占用了串口设备")
        print("建议使用UDP连接或停止mavlink-routerd服务")
        return None

    # 尝试多个串口设备
    serial_devices = ["/dev/ttyACM0", "/dev/ttyACM1", "/dev/ttyUSB0"]

    for device in serial_devices:
        try:
            print(f"尝试连接串口: {device}...")
            master = mavutil.mavlink_connection(device, baud=115200)

            # 等待心跳，设置10秒超时
            print(f"等待{device}心跳...")
            start_time = time.time()
            while time.time() - start_time < 10:
                try:
                    master.wait_heartbeat(timeout=1)
                    if master.target_system != 0:
                        print(
                            f"串口连接成功，设备：{device}，系统ID：{master.target_system}"
                        )
                        return master
                except:
                    continue

            print(f"{device}连接超时")
            master.close()

        except Exception as e:
            print(f"{device}连接失败: {e}")
            continue

    return None


def try_connect_correct_udp():
    """尝试连接正确的UDP端口"""
    try:
        print("尝试连接正确的UDP端口: 127.0.0.1:14555...")
        master = mavutil.mavlink_connection("udp:127.0.0.1:14555")

        # 设置较短的超时时间来快速检测连接
        master.mav.srcSystem = 255
        master.mav.srcComponent = 0

        # 等待心跳，设置5秒超时
        print("等待UDP心跳...")
        start_time = time.time()
        while time.time() - start_time < 5:
            master.wait_heartbeat(timeout=1)
            if master.target_system != 0:
                print(f"UDP连接成功，系统ID：{master.target_system}")
                return master

        print("UDP连接超时")
        return None

    except Exception as e:
        print(f"UDP连接失败: {e}")
        return None


def get_all_channels(msg):
    """获取所有16个通道的值"""
    return [
        msg.chan1_raw,
        msg.chan2_raw,
        msg.chan3_raw,
        msg.chan4_raw,
        msg.chan5_raw,
        msg.chan6_raw,
        msg.chan7_raw,
        msg.chan8_raw,
        msg.chan9_raw,
        msg.chan10_raw,
        msg.chan11_raw,
        msg.chan12_raw,
        msg.chan13_raw,
        msg.chan14_raw,
        msg.chan15_raw,
        msg.chan16_raw,
    ]


def get_valid_channels(msg):
    """获取有效通道（值不为0的通道）"""
    all_channels = get_all_channels(msg)
    valid_channels = []

    for i, ch_value in enumerate(all_channels, 1):
        if ch_value != 0:  # 0表示无效通道
            valid_channels.append((i, ch_value))

    return valid_channels


def check_signal_status(msg, time_delta):
    """检查SBUS信号状态"""
    status_parts = []

    # 获取所有有效通道
    valid_channels = get_valid_channels(msg)
    valid_values = [ch[1] for ch in valid_channels]  # 只取通道值

    # 检查RSSI值（ArduPilot特有）
    rssi_status = ""
    if hasattr(msg, "rssi"):
        if msg.rssi == 255:
            rssi_status = "❌ 无RC信号(RSSI=255)"
        elif msg.rssi == 0:
            rssi_status = "⚠️ RC信号弱(RSSI=0)"
        else:
            rssi_status = f"📶 RC信号强度:{msg.rssi}"

    # 检查通道数量
    chancount_status = f"📊 {len(valid_channels)}个有效通道"

    # 如果没有有效通道
    if len(valid_channels) == 0:
        status_parts.append("❌ 无有效通道")
    else:
        # 检查是否所有有效通道都是相同值（SBUS断连的主要判断条件）
        if len(set(valid_values)) == 1:
            common_value = valid_values[0]
            if common_value == 1500:
                status_parts.append("❌ SBUS断连(所有通道=1500)")
            else:
                status_parts.append(f"❌ SBUS断连(所有通道={common_value})")
        else:
            # 检查通道值是否在正常范围内
            normal_range_count = sum(1 for ch in valid_values if 1000 <= ch <= 2000)
            if normal_range_count < len(valid_values):
                out_of_range = len(valid_values) - normal_range_count
                status_parts.append(f"⚠️ {out_of_range}个通道超出范围")

            # 检查数据更新频率
            if time_delta > 0.1:  # 超过100ms没有新数据
                status_parts.append(f"⚠️ 数据延迟{time_delta*1000:.0f}ms")

            # 如果没有问题，显示正常状态
            if not status_parts:
                # 计算通道变化范围来判断信号活跃度
                ranges = [abs(ch - 1500) for ch in valid_values]
                max_range = max(ranges) if ranges else 0
                if max_range < 10:
                    status_parts.append("📡 SBUS正常(无操作)")
                else:
                    status_parts.append("📡 SBUS正常(有操作)")

    # 组合所有状态信息
    all_status = []
    if rssi_status:
        all_status.append(rssi_status)
    if chancount_status:
        all_status.append(chancount_status)
    if status_parts:
        all_status.extend(status_parts)

    return " | ".join(all_status)


def connect_mavlink():
    """自动选择连接方式"""
    # 首先尝试UDP连接（故意改错的端口）
    master = try_connect_udp()
    if master:
        return master, "UDP"

    # 检查是否有mavlink-routerd在运行
    if check_mavlink_routerd():
        print("\n发现mavlink-routerd正在运行！")
        print("mavlink-routerd通常会将串口数据转发到UDP端口14555")

        # 尝试连接正确的UDP端口
        master = try_connect_correct_udp()
        if master:
            return master, "UDP (通过mavlink-routerd)"

    # UDP失败后尝试串口连接
    master = try_connect_serial()
    if master:
        return master, "Serial"

    # 都失败了
    print("\n所有连接方式都失败了！")
    print("可能的解决方案：")
    print("1. 检查飞控是否正确连接")
    print("2. 停止mavlink-routerd服务: sudo systemctl stop mavlink-routerd")
    print("3. 检查UDP端口是否正确")
    return None, None


# 自动连接
master, connection_type = connect_mavlink()

if master is None:
    print("无法连接到飞控，退出程序")
    sys.exit(1)

print(f"使用 {connection_type} 连接成功！")

# 读取通道数据和状态消息
try:
    last_msg_time = time.time()
    signal_timeout = 3.0  # 3秒无数据认为信号丢失
    last_channels = None
    no_signal_count = 0
    failsafe_status = "未知"
    last_statustext_time = 0

    print("开始监听MAVLink消息...")
    print("监听类型: RC_CHANNELS (通道数据) + STATUSTEXT (状态消息)")
    print("-" * 80)

    while True:
        # 接收所有类型的消息，不阻塞
        any_msg = master.recv_match(blocking=False)
        current_time = time.time()

        if any_msg:
            # 处理RC_CHANNELS消息
            if any_msg.get_type() == "RC_CHANNELS":
                last_msg_time = current_time
                no_signal_count = 0

                # 获取所有有效通道
                valid_channels = get_valid_channels(any_msg)

                # 检查信号状态
                signal_status = check_signal_status(
                    any_msg, current_time - last_msg_time
                )

                # 显示所有有效通道数据
                if valid_channels:
                    # 格式化通道显示
                    channel_display = ", ".join(
                        [f"Ch{ch_num}:{ch_val:4d}" for ch_num, ch_val in valid_channels]
                    )
                    print(
                        f"{channel_display} | {signal_status} | Failsafe: {failsafe_status}"
                    )
                else:
                    print(f"无有效通道 | {signal_status} | Failsafe: {failsafe_status}")

                last_channels = [ch[1] for ch in valid_channels]

            # 处理STATUSTEXT消息（ArduPilot状态消息）
            elif any_msg.get_type() == "STATUSTEXT":
                status_text = any_msg.text.strip()
                severity = any_msg.severity

                # 检查是否是failsafe相关消息
                failsafe_keywords = [
                    "Radio Failsafe",
                    "RC Failsafe",
                    "Lost RC receiver",
                    "RC receiver lost",
                    "Failsafe",
                    "RC lost",
                    "Radio lost",
                    "RC timeout",
                    "Radio timeout",
                    "SBUS lost",
                ]

                is_failsafe_msg = any(
                    keyword.lower() in status_text.lower()
                    for keyword in failsafe_keywords
                )

                if is_failsafe_msg:
                    # 更新failsafe状态
                    if any(
                        word in status_text.lower()
                        for word in ["lost", "timeout", "failsafe"]
                    ):
                        failsafe_status = "❌ 激活"
                    else:
                        failsafe_status = "✅ 恢复"

                    # 显示failsafe消息
                    severity_icon = (
                        "🔴" if severity <= 3 else "🟡" if severity <= 5 else "🟢"
                    )
                    print(f"\n{severity_icon} FAILSAFE消息: {status_text}")
                    print(f"   严重级别: {severity}, 状态: {failsafe_status}")
                    print("-" * 80)
                    last_statustext_time = current_time

        else:
            # 没有收到消息
            no_signal_count += 1
            time_since_last = current_time - last_msg_time

            if time_since_last > signal_timeout:
                print(f"⚠️  MAVLink信号丢失 {time_since_last:.1f}秒 - 检查连接")

            # 短暂休眠避免CPU占用过高
            time.sleep(0.01)

except KeyboardInterrupt:
    print("\n程序被用户中断")
except Exception as e:
    print(f"读取数据时发生错误: {e}")
finally:
    if master:
        master.close()
    print("连接已关闭")
