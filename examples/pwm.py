import wiringpi


# 定义
OUTPUT = 1  # 表示引脚设置为输出模式
PIN_TO_PWM = 22  # 使用 wiringPi 引脚编号系统中的（物理引脚可能不同）

# 初始化 wiringpi
wiringpi.wiringPiSetup()

# 设置引脚模式为输出
wiringpi.pinMode(PIN_TO_PWM, OUTPUT)

# 创建软件PWM
wiringpi.softPwmCreate(PIN_TO_PWM, 0, 100)
# 参数1: 要控制的引脚
# 参数2: 初始值（占空比0% - LED完全关闭）
# 参数3: PWM范围（0-100，表示占空比百分比）

# 执行4次呼吸灯循环
for time in range(0, 10):
    # 渐亮过程：从0%到100%占空比
    for brightness in range(0, 100):
        wiringpi.softPwmWrite(PIN_TO_PWM, brightness)  # 设置当前占空比
        wiringpi.delay(10)  # 延迟10毫秒（注意：delay单位是毫秒）

    # 渐暗过程：从100%到0%占空比
    for brightness in reversed(range(0, 100)):
        wiringpi.softPwmWrite(PIN_TO_PWM, brightness)
        wiringpi.delay(10)
