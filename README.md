# 绞车控制系统

这是一个基于Python的专业绞车控制系统，采用模块化设计，支持Web界面控制和MAVLink遥控器控制。系统具有实时监控、急停保护、PWM输出等功能。

## ✨ 功能特点

### 核心功能
- 🎛️ **双控制模式** - Web界面控制 + MAVLink遥控器控制
- ⚡ **实时通信** - 基于WebSocket的实时数据传输
- 🛡️ **安全保护** - GPIO急停功能，确保操作安全
- 📊 **状态监控** - 实时监控电机状态、温度、扭矩等参数
- 🔧 **PWM输出** - 支持双路PWM信号输出控制

### 技术特性
- 🏗️ **模块化架构** - 清晰的代码组织和模块分离
- ⚙️ **配置管理** - 基于TOML的灵活配置系统
- 🌐 **Web服务** - 基于FastAPI的高性能Web服务器
- 📡 **MAVLink通信** - 支持多种连接方式（UDP/串口）
- 🎨 **现代界面** - 响应式Web界面，支持移动设备

## 🖥️ 系统要求

- **Python**: 3.10+
- **硬件**: OrangePi Zero2W/Zero3 或兼容的Linux单板计算机
- **操作系统**: Linux (Ubuntu/Debian推荐)
- **内存**: 最少512MB RAM
- **存储**: 最少2GB可用空间

## 🚀 快速开始

### 1. 获取代码

```bash
git clone <repository_url>
cd linux-dm-winch-test
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip3 install -r requirements.txt

# 或者使用虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 3. 配置系统

系统使用TOML格式的配置文件，首次运行会自动创建默认配置：

- `config.toml` - 主配置文件（电机参数、GPIO设置、网络配置等）

### 4. 启动系统

```bash
# 方式1：直接运行
python3 main.py

# 方式2：使用启动脚本
./scripts/start.sh

# 方式3：后台运行
nohup python3 main.py > winch.log 2>&1 &
```

## 📦 编译打包

### 智能增量编译系统

项目提供了先进的编译打包功能，支持增量编译，将Python代码编译成高性能的.so文件：

#### 🚀 编译特性

- ⚡ **增量编译** - 只编译变化的文件，大幅提升编译速度
- 🔄 **智能缓存** - 自动检测文件变化，复用已编译文件
- 🏃 **并行编译** - 使用多核CPU并行编译，提升效率
- 🔐 **权限管理** - 自动设置777权限，确保运行正常
- 📊 **详细日志** - 实时显示编译进度和状态

#### 📋 编译命令

```bash
# 增量编译（推荐，首次使用会自动完整编译）
sudo python3 build_package.py

# 强制完整重新编译（清理缓存）
sudo python3 build_package.py --clean
```

#### 🎯 编译输出

编译完成后会在以下位置生成编译版本：

- **系统目录**: `/opt/OPT-DM-WINCH-LINUX` - 生产环境部署目录
- **工程目录**: `./install/OPT-DM-WINCH-LINUX` - 本地备份和测试目录

#### ✨ 编译版本特点

- ✅ **源码保护** - 所有Python代码编译成.so文件，无法直接查看源码
- ✅ **性能优化** - 编译优化提升运行性能
- ✅ **完整功能** - 保留原项目的所有功能
- ✅ **独立运行** - 包含运行脚本和配置文件
- ✅ **易于部署** - 可直接复制到其他相同架构的设备

#### 🔧 增量编译工作原理

1. **文件检测** - 使用MD5哈希检测文件变化
2. **缓存管理** - 保存编译缓存到 `.build_cache` 文件
3. **智能决策** - 根据文件变化情况选择编译策略：
   - 无变化 → 跳过编译，直接打包
   - 有变化 → 只编译变化的文件
   - 首次编译 → 完整编译所有文件

#### 📊 性能对比

| 编译类型 | 文件变化 | 编译时间 | 说明 |
|---------|---------|---------|------|
| 首次编译 | 全部文件 | ~2-3分钟 | 编译所有7个模块 |
| 增量编译 | 1个文件 | ~30秒 | 只编译变化的文件 |
| 跳过编译 | 无变化 | ~10秒 | 直接复用已编译文件 |

### 编译版本使用

```bash
# 方式1：使用系统目录（生产环境）
cd /opt/OPT-DM-WINCH-LINUX
python3 run.py

# 方式2：使用工程install目录（测试环境）
cd ./install/OPT-DM-WINCH-LINUX
python3 run.py

# 方式3：使用启动脚本
cd /opt/OPT-DM-WINCH-LINUX
./start.sh
```

### 开发工作流

#### 日常开发（推荐）

```bash
# 1. 修改源代码
vim lib/control/control_handler.py

# 2. 增量编译（只编译变化的文件）
sudo python3 build_package.py

# 3. 测试编译版本
cd install/OPT-DM-WINCH-LINUX && python3 run.py
```

#### 重大更新

```bash
# 1. 清理缓存，强制完整重新编译
sudo python3 build_package.py --clean

# 2. 部署到生产环境
cd /opt/OPT-DM-WINCH-LINUX && python3 run.py
```

#### 🔍 编译状态说明

编译过程中会显示详细的状态信息：

```bash
🔍 检查文件变化...
  📝 修改文件: 1 个
检测到 1 个文件有变化:
  - main.py
将进行增量编译
复制已编译的.so文件...
📝 增量编译模式：只编译变化的文件
  将编译: main.py
```

### 5. 访问界面

打开浏览器访问：`http://<设备IP>:8000`

默认端口：8000

## 📖 使用说明

### Web界面控制

1. **访问界面** - 在浏览器中打开 `http://<设备IP>:8000`
2. **选择控制模式** - 切换PC控制或遥控器控制模式
3. **速度控制** - 使用滑块或输入框设置电机速度
4. **状态监控** - 实时查看电机状态、温度、扭矩等参数
5. **急停功能** - 紧急情况下使用急停按钮

### 遥控器控制

1. **连接设置** - 确保MAVLink设备正确连接
2. **通道映射** - 配置遥控器通道对应功能
3. **安全设置** - 配置失控保护和急停功能

### 配置说明

主要配置项：
- `motor_id` - 电机CAN ID
- `max_speed` - 最大速度限制
- `emergency_pin` - 急停GPIO引脚
- `pwm1_pin`, `pwm2_pin` - PWM输出引脚
- `mavlink_connections` - MAVLink连接配置

## 🏗️ 项目架构

### 目录结构

```
linux-dm-winch-test/
├── main.py                    # 🎯 主入口文件
├── config.toml               # ⚙️ 配置文件
├── requirements.txt          # 📦 依赖列表
├── pyproject.toml           # 🎨 代码格式化配置
├── build_package.py         # 📦 智能增量编译脚本
├── .build_cache             # 🗂️ 编译缓存文件（自动生成）
├──
├── lib/                     # 📚 核心库目录
│   ├── hardware/            # 🔧 硬件控制模块
│   │   ├── motor_control.py     # 电机控制
│   │   ├── gpio_control.py      # GPIO控制
│   │   └── mavlink_handler.py   # MAVLink通信
│   ├── control/             # 🎮 控制逻辑模块
│   │   └── control_handler.py   # 遥控器和PC控制
│   ├── web/                 # 🌐 Web服务模块
│   │   ├── server.py            # Web服务器
│   │   └── static/              # 静态文件
│   └── utils/               # 🛠️ 工具模块
│       └── config_manager.py    # 配置管理
├──
├── install/                 # 📁 编译输出目录
│   └── OPT-DM-WINCH-LINUX/     # 编译版本（本地）
├── examples/                # 📝 示例代码
├── scripts/                 # 🚀 脚本目录
├── tests/                   # 🧪 测试目录
└── docs/                    # 📄 文档目录
```

### 编译输出结构

```
/opt/OPT-DM-WINCH-LINUX/     # 🏭 生产环境目录
├── main.cpython-310-aarch64-linux-gnu.so    # 编译后的主程序
├── lib/                     # 编译后的库文件
│   ├── hardware/
│   │   ├── *.so                # 硬件控制模块（编译版）
│   │   └── __init__.py
│   ├── control/
│   │   ├── *.so                # 控制逻辑模块（编译版）
│   │   └── __init__.py
│   ├── web/
│   │   ├── *.so                # Web服务模块（编译版）
│   │   ├── static/             # 静态文件
│   │   └── __init__.py
│   └── utils/
│       ├── *.so                # 工具模块（编译版）
│       └── __init__.py
├── run.py                   # 🚀 编译版启动脚本
├── start.sh                 # 🎬 系统启动脚本
├── config.toml             # ⚙️ 配置文件
└── requirements.txt        # 📦 依赖列表
```

### 模块说明

- **lib.hardware** - 硬件接口层，包含电机、GPIO、MAVLink等硬件控制
- **lib.control** - 控制逻辑层，处理遥控器和PC控制逻辑
- **lib.web** - Web服务层，提供HTTP和WebSocket服务
- **lib.utils** - 工具层，提供配置管理等通用功能

## 🔧 开发指南

### 开发环境设置

#### 源码开发模式

```bash
# 1. 克隆项目
git clone <repository_url>
cd linux-dm-winch-test

# 2. 安装依赖
pip3 install -r requirements.txt

# 3. 直接运行源码版本（开发调试）
python3 main.py
```

#### 编译测试模式

```bash
# 1. 修改源码后编译测试
sudo python3 build_package.py

# 2. 测试编译版本
cd install/OPT-DM-WINCH-LINUX && python3 run.py
```

### 添加新功能

1. **硬件功能** - 在 `lib/hardware/` 中添加新的硬件接口
2. **控制逻辑** - 在 `lib/control/` 中扩展控制算法
3. **Web接口** - 在 `lib/web/` 中添加新的API端点
4. **配置项** - 在 `lib/utils/config_manager.py` 中添加配置管理

### 开发最佳实践

#### 🔄 开发-编译-测试循环

```bash
# 1. 开发阶段：使用源码版本快速调试
python3 main.py

# 2. 功能完成：编译测试性能和兼容性
sudo python3 build_package.py

# 3. 部署阶段：使用编译版本
cd /opt/OPT-DM-WINCH-LINUX && python3 run.py
```

#### 📝 编译优化建议

- **频繁修改时** - 使用源码版本开发，避免频繁编译
- **功能测试时** - 使用增量编译快速验证
- **性能测试时** - 使用编译版本获得最佳性能
- **生产部署时** - 使用完整编译版本确保稳定性

#### 🗂️ 缓存管理

```bash
# 查看编译缓存状态
cat .build_cache

# 清理缓存（强制完整重新编译）
sudo python3 build_package.py --clean

# 手动删除缓存文件
rm .build_cache
```

### 代码规范

```bash
# 代码格式化
black --line-length 88 .

# 代码检查
flake8 --max-line-length=88 --ignore=E203,W503,E722 .
```

### 测试

```bash
# 运行测试
python -m pytest tests/

# 测试覆盖率
python -m pytest --cov=lib tests/

# 测试编译版本
cd install/OPT-DM-WINCH-LINUX
python3 -c "import lib.utils.config_manager; print('编译版本导入测试成功')"
```

## 🔍 故障排除

### 常见问题

#### 🐛 运行时问题

**Q: 程序启动失败，提示模块导入错误**
```bash
# 确保在项目根目录运行
cd /path/to/linux-dm-winch-test
python3 main.py
```

**Q: 编译版本提示 "No module named 'lib.xxx'"**
```bash
# 重新进行完整编译
sudo python3 build_package.py --clean

# 检查编译输出目录是否完整
ls -la /opt/OPT-DM-WINCH-LINUX/lib/*/
```

**Q: Web界面无法访问**
```bash
# 检查防火墙设置
sudo ufw allow 8000
# 或者检查服务是否正常启动
ps aux | grep python
```

**Q: MAVLink连接失败**
```bash
# 检查设备权限
sudo chmod 666 /dev/ttyACM0
# 检查设备是否存在
ls -la /dev/tty*
```

**Q: GPIO权限问题**
```bash
# 添加用户到gpio组
sudo usermod -a -G gpio $USER
# 重新登录后生效
```

#### 🔨 编译问题

**Q: 编译失败，提示权限不足**
```bash
# 使用sudo运行编译脚本
sudo python3 build_package.py

# 检查/opt目录权限
ls -la /opt/
```

**Q: 编译速度很慢**
```bash
# 检查是否启用了增量编译
# 首次编译较慢是正常的，后续会很快

# 查看CPU使用情况
htop
```

**Q: 增量编译不生效，每次都完整编译**
```bash
# 检查缓存文件是否存在
ls -la .build_cache

# 清理缓存重新开始
sudo python3 build_package.py --clean
```

**Q: 编译后文件权限问题**
```bash
# 脚本会自动设置777权限
# 如果仍有问题，手动设置：
sudo chmod -R 777 /opt/OPT-DM-WINCH-LINUX
sudo chmod -R 777 install/OPT-DM-WINCH-LINUX
```

**Q: 缺少Cython依赖**
```bash
# 脚本会自动安装Cython
# 如果失败，手动安装：
pip3 install Cython

# 或者使用系统包管理器
sudo apt install cython3
```

### 日志查看

```bash
# 查看实时日志
tail -f winch.log

# 查看错误日志
grep ERROR winch.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与联系

- **问题反馈**: 请在 GitHub Issues 中提交
- **功能建议**: 欢迎提交 Feature Request
- **技术讨论**: 欢迎在 Discussions 中交流

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**⚠️ 安全提醒**: 请在使用前仔细阅读安全说明，确保急停功能正常工作，避免设备损坏或人身伤害。
