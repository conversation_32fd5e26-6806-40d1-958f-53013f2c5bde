#!/usr/bin/env python3
"""
OPT-DM-WINCH-LINUX 运行脚本
编译版本入口文件
"""

import sys
import os
import uvicorn
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        # 导入编译后的主模块和必要的函数
        from lib.utils.config_manager import get_default_config, save_config

        # 确保配置文件存在
        default_config = get_default_config()
        if not Path("config.toml").exists():
            save_config(default_config)

        # 导入主模块以获取app对象
        import main

        logger.info("🚀 启动 OPT-DM-WINCH-LINUX 编译版本")

        # 启动服务器
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            lifespan="on",  # 明确启用 lifespan
        )

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，程序正在退出...")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有编译文件完整")
        print("当前目录内容:")
        for item in current_dir.iterdir():
            print(f"  {item.name}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 程序退出处理现在由FastAPI的lifespan管理
        logger.info("run.py: 程序退出，清理工作由FastAPI lifespan处理")

if __name__ == "__main__":
    main()
