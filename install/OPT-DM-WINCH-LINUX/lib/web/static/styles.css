* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html,
body {
    height: 100%;
    width: 100%;
    font-family: Arial, sans-serif;
    background-color: #e6ffe6;
    font-size: 16px;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 15px;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
}

.logo {
    max-width: 200px;
    margin: 0 auto 20px;
}

h1,
h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* 修改放置在标题旁的急停状态样式 */
h2 .status-indicator {
    font-size: 16px;
    padding: 5px 10px;
    border-radius: 5px;
    vertical-align: middle;
}

.content {
    flex: 1;
    display: flex;
    gap: 20px;
    height: calc(100vh - 200px);
    width: 100%;
}

.control-panel,
.data-panel,
.login-panel {
    background-color: #f0f8ff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.control-panel,
.data-panel {
    flex: 1;
    min-width: 400px;
    height: 100%;
}

.control-group {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

button,
input[type="number"],
input[type="text"],
input[type="password"],
select {
    padding: 12px;
    font-size: 20px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s;
    flex: 1;
}

button:hover {
    background-color: #45a049;
}

input[type="number"],
input[type="text"],
input[type="password"],
select {
    text-align: center;
    background-color: #f0f0f0;
    color: black;
}

#status {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    font-weight: bold;
    color: #333;
    text-align: center;
    display: none;
    animation: fadeIn 0.3s ease-in-out;
    z-index: 1000;
    max-width: 80%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateX(-50%) translateY(20px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

#motorData {
    display: flex;
    flex-direction: column;
    gap: 15px;
    font-size: 20px;
    flex: 1;
    justify-content: space-between;
}

.data-item {
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s;
    font-size: 20px;
    min-height: 60px; /* 确保每项高度一致 */
}

/* 确保数据项中的所有span元素都正确显示 */
.data-item span {
    display: inline-block;
}

.data-label {
    font-weight: bold;
    font-size: 20px;
    flex: 0 0 160px; /* 固定宽度，不伸缩 */
    margin-right: 10px; /* 添加右侧间距 */
}

/* 确保各数据项的值正确显示 */
.data-item .status-indicator,
.data-item #totalRounds,
.data-item #angle,
.data-item #speed,
.data-item #torque,
.data-item #temperature,
.data-item #emergencyStopStatus {
    flex: 1; /* 让值占据剩余空间 */
    text-align: right; /* 右对齐值 */
    word-break: normal; /* 不允许单词中间换行 */
    white-space: nowrap; /* 不允许文本换行 */
}

/* 添加状态指示器的样式 */
.status-indicator {
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    display: inline-block; /* 确保只覆盖文字 */
    width: auto; /* 宽度自适应内容 */
    margin-left: auto; /* 将状态指示器推到右侧 */
    max-width: fit-content; /* 确保最大宽度适合内容 */
}

/* 失能状态样式 */
.status-indicator.status-error {
    background-color: #f44336;
    color: white;
    width: auto; /* 只覆盖文字宽度 */
    box-sizing: content-box; /* 确保宽度只计算内容 */
}

/* 使能状态样式 */
.status-indicator.enabled {
    background-color: #4CAF50;
    color: white;
    width: auto; /* 只覆盖文字宽度 */
    box-sizing: content-box; /* 确保宽度只计算内容 */
}

/* 急停状态样式 */
.status-indicator.status-ok {
    background-color: #4CAF50;
    color: white;
}

/* 急停激活状态样式 */
.status-indicator.status-error {
    background-color: #f44336;
    color: white;
}

.data-item:hover {
    background-color: #45a049;
}

.password-container {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 15px;
    cursor: pointer;
    font-size: 28px;
}

#loginPanel {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 400px;
    margin: 0 auto;
}

#controlPanel {
    display: none;
}

.disabled {
    background-color: #FF6666;
    opacity: 0.6;
    pointer-events: none;
}

.disabled input,
.disabled button {
    background-color: #f0f0f0 !important;
    color: #888 !important;
    border-color: #ccc !important;
}

.disabled_control_mode {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

#joystick-container {
    width: 300px;
    height: 300px;
    background-color: #f0f0f0;
    border-radius: 50%;
    position: relative;
    margin: 20px auto;
    flex-shrink: 0;
    /* 防止容器缩小 */
}

#joystick {
    width: 100px;
    height: 100px;
    background-color: #4CAF50;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

#joystick-speed {
    text-align: center;
    font-size: 20px;
    margin-top: 10px;
}

/* 响应式设计 - 从大屏幕到小屏幕 */
@media (max-width: 1200px) {
    .content {
        flex-direction: column;
    }

    .control-panel,
    .data-panel {
        width: 100%;
        min-width: unset;
    }
}

/* 平板设备 */
@media (max-width: 992px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 28px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .control-panel, .data-panel {
        padding: 20px;
    }
    
    .config-item {
        grid-template-columns: 1fr;
    }
    
    .config-item label {
        text-align: left;
        border-right: none;
        padding-right: 0;
        margin-bottom: 5px;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .sidebar-toggle {
        left: 200px;
    }
    
    .page {
        margin-left: 200px;
        width: calc(100% - 200px);
    }
    
    .control-group {
        flex-direction: column;
    }
    
    button, input[type="number"], select {
        font-size: 16px;
        padding: 10px;
    }
    
    .data-item {
        font-size: 16px;
        padding: 10px;
    }
    
    .data-label {
        font-size: 16px;
        flex: 0 0 120px;
    }
    
    .network-status, .language-switch {
        position: static;
        margin-top: 10px;
        align-items: center;
    }
    
    .network-status {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
    }
}

/* 小型移动设备 */
@media (max-width: 576px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        padding: 10px;
    }
    
    .sidebar:not(.collapsed) {
        height: 100vh;
    }
    
    .sidebar.collapsed {
        height: auto;
        left: 0;
        padding-bottom: 0;
    }
    
    .sidebar-toggle {
        position: relative;
        left: auto;
        top: auto;
        margin: 10px 0;
        width: 100%;
        border-radius: 5px;
        text-align: center;
    }
    
    .sidebar-toggle.collapsed {
        left: auto;
    }
    
    .page {
        margin-left: 0;
        width: 100%;
        margin-top: 60px;
    }
    
    .sidebar.collapsed ~ .page {
        margin-top: 60px;
    }
    
    .company-logo {
        text-align: center;
        margin-bottom: 10px;
    }
    
    .nav-btn {
        display: none;
    }
    
    .sidebar:not(.collapsed) .nav-btn {
        display: block;
    }
    
    h1 {
        font-size: 24px;
        margin-top: 10px;
    }
    
    .logo {
        max-width: 150px;
    }
    
    .control-panel, .data-panel {
        padding: 15px;
    }
    
    #motorData {
        gap: 10px;
    }
    
    .emergency-status {
        text-align: center;
        margin-bottom: 10px;
    }
}

.language-switch {
    position: absolute;
    top: 15px;
    right: 15px;
}

#langToggle {
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
}

.status-indicator {
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
}

.enabled {
    background-color: #4CAF50;
    color: white;
}

.network-status {
    position: absolute;
    top: 60px;
    /* 在语言切换按钮下方 */
    right: 15px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.network-status .status-indicator {
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    background-color: #4CAF50;
    color: white;
}

.sidebar {
    width: 250px;
    background-color: #80b8f0;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    color: white;
    padding-top: 20px;
    transition: all 0.3s ease;
    z-index: 1000;
    overflow: hidden;
}

.sidebar.collapsed {
    left: -250px;
}

.sidebar-toggle {
    position: fixed;
    left: 250px;
    top: 20px;
    background: #2c3e50;
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
    transition: all 0.3s ease;
}

.sidebar-toggle.collapsed {
    left: 0;
}

.nav-btn {
    display: block;
    width: 100%;
    padding: 15px 20px;
    text-align: left;
    color: white;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    margin: 0;
}

.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-btn.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: bold;
}

.nav-btn.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: white;
}

.nav {
    margin-bottom: 30px;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.nav-btn {
    padding: 10px 20px;
    font-size: 16px;
    margin: 0 10px;
    cursor: pointer;
    border: none;
    background: none;
    position: relative;
}

.nav-btn.active {
    color: #2196F3;
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #dde3e9;
}

.sbus-container {
    margin: 20px;
    padding: 10px;
}

.channel-bar {
    margin: 10px 0;
    padding: 5px;
    background: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    min-height: 40px;
}

.channel-label {
    display: inline-block;
    width: 100px;
    text-align: right;
    margin-right: 10px;
    font-weight: bold;
}

.progress-bar {
    display: inline-block;
    width: 600px;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    vertical-align: middle;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #2196F3;
    width: 0%;
    transition: width 0.2s ease;
}

.current-marker {
    position: absolute;
    width: 3px;
    height: 26px;
    background: #ff4444;
    top: -3px;
    left: 0;
    transition: left 0.1s ease;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    transform: translateX(-50%);
    z-index: 2;
}

.calibration-range {
    position: absolute;
    height: 100%;
    background: rgba(76, 175, 80, 0.2);
    transition: all 0.3s ease;
    display: none;
}

.current-raw {
    display: inline-block;
    width: 100px;
    margin-left: 10px;
    font-size: 12px;
    color: #6c757d;
}

.channel-value {
    display: inline-block;
    width: 60px;
    margin-left: 10px;
    text-align: left;
    font-weight: bold;
    color: #495057;
}

.calibrating .progress-fill {
    background: #90caf9;
}

.save-button {
    background-color: #4CAF50;
    display: none;
}

.save-button.visible {
    display: inline-block;
}

.sbus-status {
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 20px;
}

/* .status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
} */

.status-indicator-sbus {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator-sbus.status-ok {
    background-color: #28a745;
}

.status-indicator-sbus.status-error {
    background-color: #dc3545;
}

.status-text {
    font-size: 14px;
    color: #495057;
}

.status-ok {
    background-color: #4CAF50;
}

.status-error {
    background-color: #f44336;
}

.calibration-controls {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.calibration-controls button {
    margin-right: 10px;
    min-width: 120px;
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

#calButton {
    background-color: #2196F3;
}

#calButton:hover:not(:disabled) {
    background-color: #0b7dda;
}

#saveButton {
    background-color: #4CAF50;
}

#saveButton:hover:not(:disabled) {
    background-color: #45a049;
}

#resetButton {
    background-color: #f44336;
}

#resetButton:hover:not(:disabled) {
    background-color: #d32f2f;
}

.calibration-controls button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.5;
}

#calStatus {
    font-weight: bold;
    color: #2196F3;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.channel-limits {
    font-size: 14px;
    color: #666;
    margin-left: 10px;
    display: none;
    /* 默认隐藏 */
}

.calibrating .channel-limits {
    display: inline-block;
    /* 校准时显示 */
    color: #2196F3;
    font-weight: bold;
}

.current-raw {
    font-size: 16px;
    color: #ff4444;
    margin-left: 10px;
    display: none;
    /* 默认隐藏 */
}

.calibrating .current-raw {
    display: inline-block;
    /* 校准时显示 */
}

.calibrating {
    animation: pulse 1s infinite;
}

.sbus-alert {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    text-align: center;
    animation: pulse 2s infinite;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sbus-alert p {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calibration-controls {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    align-items: center;
}

.calibration-controls button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.calibration-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.page {
    display: none;
    padding: 20px;
    margin-left: 250px;
    width: calc(100% - 250px);
    transition: all 0.3s ease;
}

.page.active {
    display: block;
}

#control_monitor-page.active {
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed~.page {
    margin-left: 0;
    width: 100%;
}

#sbus-page {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#config-page {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.config-panel {
    background-color: #f0f8ff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

.config-item {
    display: grid;
    grid-template-columns: 300px 1fr;
    align-items: center;
    gap: 20px;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.config-item label {
    font-size: 20px;
    color: #333;
    font-weight: 500;
    text-align: right;
    padding-right: 20px;
    border-right: 2px solid #e0e0e0;
}

.config-item input {
    font-size: 20px;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    width: 200px;
    text-align: center;
    transition: border-color 0.3s;
    background-color: #f8f9fa;
}

.config-item input:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.2);
}

.config-item input::placeholder {
    color: #999;
}

/* 配置按钮样式 */
.control-group {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.button.config {
    font-size: 20px;
    padding: 12px 30px;
    min-width: 150px;
    border-radius: 8px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.button.config:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button.config:active {
    transform: translateY(0);
}

/* 添加响应式布局 */
@media (max-width: 768px) {
    .config-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .config-item label {
        text-align: left;
        border-right: none;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 10px;
    }

    .config-item input {
        width: 100%;
    }
}

/* 修改公司logo样式 */
.company-logo {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.company-logo a {
    color: #ffffff;
    text-decoration: none;
    font-size: 24px;
    font-weight: bold;
    display: inline-block;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.company-logo a:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-decoration: underline;
}

.company-logo a:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 修改状态指示器的样式 */
.status-indicator-sbus {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-text {
    margin-right: 20px;
}

.status-ok {
    background-color: #4CAF50;
}

.status-error {
    background-color: #f44336;
}

/* 移除旧的摇杆样式 */
/* 添加新的滑块样式 */
.speed-slider {
    width: 100%;
    height: 20px;
    margin: 20px 0;
    -webkit-appearance: none;
    background: #f0f0f0;
    border-radius: 10px;
    outline: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 30px;
    height: 30px;
    background: #4CAF50;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s;
}

.speed-slider::-webkit-slider-thumb:hover {
    background: #45a049;
}

.speed-slider::-moz-range-thumb {
    width: 30px;
    height: 30px;
    background: #4CAF50;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: background 0.3s;
}

.speed-slider::-moz-range-thumb:hover {
    background: #45a049;
}

#speed-slider-container {
    width: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
}

#slider-speed {
    text-align: center;
    font-size: 20px;
    margin-top: 10px;
    color: #333;
}

.slider-note {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
    font-style: italic;
}

/* 加载动画 */
.loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 提示框样式 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15px 25px;
    background: #333;
    color: white;
    border-radius: 5px;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    z-index: 9999;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
    display: block;
    font-size: 16px;
    font-weight: bold;
}

.toast.show {
    opacity: 1;
}

.toast.success {
    background: #2ecc71;
}

.toast.error {
    background: #e74c3c;
}

.toast.info {
    background: #3498db;
}

/* 操作确认对话框样式 */
.confirm-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: none;
}

.confirm-dialog.show {
    display: block;
}

.confirm-dialog .buttons {
    margin-top: 20px;
    text-align: right;
}

.confirm-dialog button {
    margin-left: 10px;
}

/* 添加急停状态相关样式 */
.disabled_emergency {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.status-error {
    background-color: #f44336;
    color: white;
    font-weight: bold;
}

.status-warning {
    background-color: #ff9800;
    color: white;
    font-weight: bold;
}

.toast.warning {
    background-color: #ff9800;
    color: white;
}

/* 添加急停状态的样式 */
.emergency-status {
    text-align: center;
    margin-bottom: 5px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.emergency-status .status-indicator {
    font-size: 18px;
    padding: 8px 15px;
}

.emergency-status .status-ok {
    background-color: #4CAF50;
}

.emergency-status .status-error {
    background-color: #f44336;
}

#enableStatus {
    width: auto !important; /* 强制使用自动宽度 */
    max-width: fit-content !important; /* 最大宽度适合内容 */
    float: right; /* 确保靠右对齐 */
    margin-left: auto; /* 将元素推到右侧 */
    box-sizing: content-box !important; /* 确保宽度只计算内容 */
}