"""
Web服务模块
包含所有Web相关功能：WebSocket管理、路由定义、消息处理
"""

import logging
from typing import List
from pathlib import Path
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from lib.utils.config_manager import load_config, validate_config

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        # 发送当前配置
        config = load_config()
        await websocket.send_json({"type": "config", "data": config})

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        """向所有连接的客户端广播消息"""
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except WebSocketDisconnect:
                self.disconnect(connection)
            except Exception as e:
                logger.error(f"广播消息时出错: {e}")


class WebSocketMessageHandler:
    """WebSocket消息处理器"""

    def __init__(self, pc_control_handler, motor, manager, config_lock):
        self.pc_control_handler = pc_control_handler
        self.motor = motor
        self.manager = manager
        self.config_lock = config_lock
        self.current_mode = "remote"  # 默认模式
        self.latest_config_update = None

    async def handle_message(self, data: dict, websocket: WebSocket, motor_data: dict):
        """处理WebSocket消息"""
        command = data.get("command")
        if not command:
            logger.warning("收到无效的WebSocket命令")
            return

        try:
            if command == "enable":
                self.pc_control_handler.enable_motor()

            elif command == "disable":
                self.motor.set_velocity_mode(0.0)
                self.pc_control_handler.disable_motor()

            elif command == "set_speed":
                speed = float(data.get("value", 0))
                self.pc_control_handler.set_motor_command("set_speed", speed)
                await self.manager.broadcast(
                    {"type": "motor_status", "data": motor_data}
                )

            elif command == "set_mode":
                mode = data.get("value")
                if mode in ["pc", "remote"]:
                    self.current_mode = mode
                    await self.manager.broadcast({"type": "control_mode", "mode": mode})

            elif command == "get_config":
                config = load_config()
                await websocket.send_json({"type": "config", "data": config})

            elif command == "get_config_for_save":
                config = load_config()
                updates = data.get("config", {})
                await websocket.send_json(
                    {"type": "config_for_save", "data": config, "updates": updates}
                )

            elif command == "set_config":
                new_config = data.get("config", {})
                if validate_config(new_config):
                    with self.config_lock:
                        self.latest_config_update = new_config
                    await self.manager.broadcast(
                        {"type": "config_updated", "data": new_config}
                    )
                else:
                    await websocket.send_json({"type": "error", "message": "配置无效"})

        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {e}")
            await websocket.send_json({"type": "error", "message": str(e)})

    def get_current_mode(self):
        """获取当前控制模式"""
        return self.current_mode

    def set_current_mode(self, mode: str):
        """设置当前控制模式"""
        self.current_mode = mode

    def get_latest_config_update(self):
        """获取最新配置更新"""
        return self.latest_config_update

    def clear_config_update(self):
        """清空配置更新"""
        self.latest_config_update = None


def create_app(
    pc_control_handler,
    motor,
    config_lock,
    lifespan_func,
    get_motor_data_func,
    get_current_mode_func,
    set_current_mode_func,
    get_latest_config_update_func,
    set_latest_config_update_func,
):
    """创建FastAPI应用"""
    app = FastAPI(lifespan=lifespan_func)

    # 创建连接管理器和消息处理器
    manager = ConnectionManager()
    message_handler = WebSocketMessageHandler(
        pc_control_handler, motor, manager, config_lock
    )

    # WebSocket路由
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await manager.connect(websocket)
        try:
            # 立即发送当前电机状态
            motor_data = get_motor_data_func()
            await websocket.send_json({"type": "motor_status", "data": motor_data})

            while True:
                data = await websocket.receive_json()
                # 处理消息时传入当前状态访问函数
                await handle_websocket_message_with_globals(
                    data,
                    websocket,
                    manager,
                    pc_control_handler,
                    motor,
                    config_lock,
                    get_motor_data_func,
                    get_current_mode_func,
                    set_current_mode_func,
                    get_latest_config_update_func,
                    set_latest_config_update_func,
                )
        except WebSocketDisconnect:
            manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket错误: {e}")
            manager.disconnect(websocket)

    # 根路由
    @app.get("/", response_class=HTMLResponse)
    async def read_root():
        try:
            html_file_path = Path(__file__).parent / "static" / "index.html"
            with open(html_file_path, "r", encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            logger.error(f"index.html not found at {html_file_path}")
            return HTMLResponse(
                content="<h1>Error: index.html not found.</h1>", status_code=404
            )
        except Exception as e:
            logger.error(f"Error serving index.html: {e}")
            return HTMLResponse(
                content=f"<h1>Error serving index.html: {e}</h1>", status_code=500
            )

    # 静态文件挂载
    static_files_directory = str(Path(__file__).parent / "static")
    app.mount("/static", StaticFiles(directory=static_files_directory), name="static")

    return app, manager, message_handler


async def handle_websocket_message_with_globals(
    data,
    websocket,
    manager,
    pc_control_handler,
    motor,
    config_lock,
    get_motor_data_func,
    get_current_mode_func,
    set_current_mode_func,
    get_latest_config_update_func,
    set_latest_config_update_func,
):
    """处理WebSocket消息（带全局状态访问）"""
    command = data.get("command")
    if not command:
        logger.warning("收到无效的WebSocket命令")
        return

    try:
        if command == "enable":
            pc_control_handler.enable_motor()

        elif command == "disable":
            motor.set_velocity_mode(0.0)
            pc_control_handler.disable_motor()

        elif command == "set_speed":
            speed = float(data.get("value", 0))
            pc_control_handler.set_motor_command("set_speed", speed)
            motor_data = get_motor_data_func()
            await manager.broadcast({"type": "motor_status", "data": motor_data})

        elif command == "set_mode":
            mode = data.get("value")
            if mode in ["pc", "remote"]:
                set_current_mode_func(mode)
                await manager.broadcast({"type": "control_mode", "mode": mode})

        elif command == "get_config":
            config = load_config()
            await websocket.send_json({"type": "config", "data": config})

        elif command == "get_config_for_save":
            config = load_config()
            updates = data.get("config", {})
            await websocket.send_json(
                {"type": "config_for_save", "data": config, "updates": updates}
            )

        elif command == "set_config":
            new_config = data.get("config", {})
            if validate_config(new_config):
                with config_lock:
                    set_latest_config_update_func(new_config)
                await manager.broadcast({"type": "config_updated", "data": new_config})
            else:
                await websocket.send_json({"type": "error", "message": "配置无效"})

    except Exception as e:
        logger.error(f"处理WebSocket消息时出错: {e}")
        await websocket.send_json({"type": "error", "message": str(e)})
