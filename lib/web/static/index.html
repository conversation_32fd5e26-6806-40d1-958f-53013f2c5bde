<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title id="pageTitle">FT120 绞车控制面板</title>
    <link rel="icon" type="image/x-icon" href="/static/assets/ocean_logo_32.ico">
    <link rel="stylesheet" href="/static/styles.css">
</head>

<body>
    <!-- 加载指示器 -->
    <div class="loading-spinner" id="loadingSpinner" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- 提示框 -->
    <div class="toast" id="toastMessage"></div>

    <!-- 状态消息 -->
    <div id="status"></div>

    <!-- 确认对话框 -->
    <div class="confirm-dialog" id="confirmDialog">
        <div class="dialog-content">
            <p id="confirmMessage"></p>
            <div class="buttons">
                <button id="cancelButton" data-i18n="cancel">取消</button>
                <button id="confirmButton" data-i18n="confirm">确认</button>
            </div>
        </div>
    </div>

    <div class="sidebar" id="sidebar">
        <div class="company-logo">
            <a href="https://oceanphysics.cn/" target="_blank" data-i18n="companyName">
                翱飞(无锡)科技有限公司
            </a>
        </div>
        <!-- <h2 style="text-align: center; margin-bottom: 30px;">FT 120绞车控制系统</h2> -->
        <button class="nav-btn active" onclick="showPage('control_monitor')" data-i18n="navControlMonitor">绞车控制数据主页</button>
        <button class="nav-btn" onclick="showPage('config')" data-i18n="navConfig">系统配置</button>
        <button class="nav-btn" onclick="showPage('sbus')" data-i18n="navChannelData">通道数据</button>
    </div>
    <button class="sidebar-toggle" id="sidebarToggle">☰</button>

    <div class="container page active" id="control_monitor-page">
        <img src="/static/assets/ocean-log.png" alt="Ocean Log" class="logo"
            onerror="this.onerror=null;this.src='';this.alt='Logo加载失败';">
        <h1 data-i18n="title">FT120 绞车控制面板</h1>
        <div class="content">
            <div class="control-panel">
                <h2 data-i18n="winchControl">绞车控制栏</h2>
                <div class="control-group">
                    <button id="enableButton">使能绞车</button>
                    <button id="disableButton">失能绞车</button>
                </div>
                <div id="pcControlGroup">
                    <div class="control-group">
                        <input type="number" id="speedInput" placeholder="速度 (rad/s)" data-i18n-placeholder="speedPlaceholder">
                        <button id="setSpeedButton" data-i18n="setSpeed">设置速度</button>
                    </div>
                </div>
                <div class="control-group">
                    <select id="controlMode">
                        <option value="pc" data-i18n="pcControl">web界面控制</option>
                        <option value="remote" data-i18n="remoteControl">遥控器控制</option>
                    </select>
                    <button id="setControlMode" data-i18n="setControlMode">设置控制模式(WEB/遥控器)</button>
                </div>
                <div id="speed-slider-container">
                    <input type="range" id="speedSlider" min="-20" max="20" value="0" step="0.1" class="speed-slider">
                    <div id="slider-speed" data-i18n="currentSpeed">当前速度: 0 rad/s</div>
                    <div class="slider-note" data-i18n="noteSliderSpeed">注：滑块速度范围受系统配置中的【绞车最大转速】限制</div>
                </div>
            </div>
            <div class="data-panel">
                <h2 data-i18n="winchStatus">绞车当前状态</h2>
                <div class="emergency-status">
                    <span id="emergencyStopStatus" class="status-indicator status-ok" data-i18n="emergencyStopInactive">急停未激活</span>
                </div>
                <div id="motorData">
                    <div class="data-item">
                        <span class="data-label" data-i18n="isEnabled">是否使能:</span>
                        <span id="enableStatus" class="status-indicator">未知</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label" data-i18n="totalRounds">总圈数:</span>
                        <span id="totalRounds">-- rounds</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label" data-i18n="currentCableLength">当前放线长度:</span>
                        <span id="angle">-- m</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label" data-i18n="speed">速度:</span>
                        <span id="speed">-- rad/s</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label" data-i18n="torque">扭矩:</span>
                        <span id="torque">-- N·m</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label" data-i18n="temperature">温度:</span>
                        <span id="temperature">-- °C</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="language-switch">
            <button id="langToggle" class="status-indicator">Switch to English</button>
        </div>
    </div>


    <div id="config-page" class="page">
        <h2 data-i18n="systemConfig">系统配置</h2>
        <div class="config-panel">
            <div class="config-group">
                <div class="config-item">
                    <label data-i18n="enableChannel">绞车使能通道</label>
                    <input type="number" id="enableChannel" min="1" max="16" placeholder="1~16">
                </div>
                <div class="config-item">
                    <label data-i18n="controlChannel">收放缆控制通道</label>
                    <input type="number" id="controlChannel" min="1" max="16" placeholder="1~16">
                </div>
                <div class="config-item">
                    <label data-i18n="maxWinchSpeed">绞车最大转速 (rad/s)</label>
                    <input type="number" id="maxSpeed" min="0" max="20" step="0.1" placeholder="1~20">
                </div>
            </div>
            <div class="control-group">
                <button id="loadConfig" class="button config" data-i18n="loadConfig">读取配置</button>
                <button id="saveConfig" class="button config" data-i18n="saveConfig">保存配置</button>
            </div>
        </div>
    </div>

    <div id="sbus-page" class="page">
        <h2 data-i18n="channelData">通道数据</h2>
        <div id="sbusSignalAlert" class="sbus-alert" style="display: none;">
            <p data-i18n="channelSignalLost">⚠️ 未检测到通道信号，请检查连接</p>
        </div>


        <div class="sbus-status">
            <p>
                <span class="status-indicator-sbus" id="failSafe"></span>
                <span class="status-text" id="failSafeText" data-i18n="failsafeStatus">故障保护状态</span>
                <span class="status-indicator-sbus" id="lostFrame"></span>
                <span class="status-text" id="lostFrameText" data-i18n="lostFrameStatus">丢帧状态</span>
            </p>
        </div>
        <div class="sbus-container" id="channelsContainer">
            <!-- 通道数据将通过JavaScript动态添加 -->
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>

</html>