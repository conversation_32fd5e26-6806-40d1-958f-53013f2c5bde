// 全局变量
let currentLang = 'zh-cn';
let ws = null;
let wsReconnectAttempts = 0;
const MAX_WS_RECONNECT_ATTEMPTS = 5;
let wsReconnectTimeout = null;
let calibrationData = {};
let lastNetworkStatus = '';
let isRemoteControlMode = false; // 跟踪当前控制模式
let lastSetSpeed = 0; // 记录最后设置的速度
let speedUpdateTimeout = null; // 用于节流速度更新请求
const SPEED_UPDATE_DELAY = 20; // 速度更新延迟时间(毫秒) - 优化为20ms实现更好的实时性
let latestMotorData = { isEnabled: false };
let targetSpeed = 0; // 添加targetSpeed变量跟踪当前目标数据
let maxConfigSpeed = 25.0; // 默认最大速度，将从配置中获取

// 添加全局变量来追踪急停状态
let emergencyStopActive = false;

// 添加标志来跟踪是否是手动请求配置
let isManualConfigRequest = false;

function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
        page.style.display = 'none';
    });

    // 更新导航按钮状态
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`.nav-btn[onclick="showPage('${pageId}')"]`).classList.add('active');

    // 显示选中的页面
    const selectedPage = document.getElementById(pageId + '-page');
    selectedPage.classList.add('active');
    selectedPage.style.display = pageId === 'control_monitor' ? 'flex' : 'block';
}

// WebSocket连接函数
function connectWebSocket() {
    if (ws) {
        ws.close();
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    console.log('正在连接到WebSocket:', wsUrl);

    ws = new WebSocket(wsUrl);

    ws.onopen = function () {
        console.log('WebSocket连接已建立');
        wsReconnectAttempts = 0;
        showToast(languages[currentLang].wsConnected, 'success', 3000, 'top-left');
        // 在连接建立时请求最新配置
        sendWebSocketCommand('get_config');
        // 请求当前电机状态
        sendWebSocketCommand('get_status');
    };

    ws.onclose = function (event) {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        if (wsReconnectAttempts < MAX_WS_RECONNECT_ATTEMPTS) {
            wsReconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, wsReconnectAttempts), 10000);
            console.log(`将在 ${delay}ms 后尝试重连...`);
            wsReconnectTimeout = setTimeout(connectWebSocket, delay);
            showToast(languages[currentLang].wsDisconnected, 'warning', 3000, 'top-left');
        } else {
            showToast(languages[currentLang].wsConnectionFailed, 'error', 3000, 'top-left');
        }
    };

    ws.onerror = function (error) {
        console.error('WebSocket错误:', error);
        showToast(languages[currentLang].wsConnectionError, 'error', 3000, 'top-left');
    };

    ws.onmessage = function (event) {
        try {
            const data = JSON.parse(event.data);
            // console.log('收到WebSocket消息:', data); // 注释掉以提高性能
            handleWebSocketMessage(data);
        } catch (error) {
            console.error('处理WebSocket消息时出错:', error);
        }
    };
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    if (!data || !data.type) return;

    switch (data.type) {
        case 'motor_status':
            updateMotorStatus(data.data);
            break;
        case 'network_status':
            updateNetworkStatus(data.data);
            break;
        case 'sbus_data':
            updateSbusData(data.data);
            break;
        case 'mavlink_status':
            updateMavlinkStatus(data.data);
            break;
        case 'emergency_stop':
            updateEmergencyStopStatus(data.data);
            break;
        case 'config':
            handleConfigUpdate(data.data);
            // 只在手动请求配置时显示提示
            if (isManualConfigRequest) {
                showToast(languages[currentLang].configLoadSuccess, 'success');
                isManualConfigRequest = false; // 重置标志
            }
            break;
        case 'error':
            showToast(data.message, 'error');
            break;
        case 'control_mode': // 添加 control_mode 处理
            updateControlPanel(data.mode);
            break;
        // 校准相关消息处理已移除 - 校准功能已禁用
        case 'config_updated':
            showToast(languages[currentLang].configSaveSuccess, 'success');
            // 重新加载配置并更新UI
            handleConfigUpdate(data.data);
            break;
        case 'config_for_save':
            // 处理保存配置的响应
            handleConfigSave(data.data, data.updates);
            break;
    }
}

// 发送WebSocket命令
function sendWebSocketCommand(command, value = null, config = null) {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
        console.error('WebSocket未连接，无法发送命令');
        showToast(languages[currentLang].wsNotConnected, 'error', 3000, 'top-left');
        return;
    }

    const message = {
        command: command,
        value: value,
        config: config
    };

    // console.log('发送WebSocket命令:', message); // 注释掉以提高性能
    ws.send(JSON.stringify(message));
}

// 更新电机状态
function updateMotorStatus(data) {
    if (!data) {
        console.warn('收到空的电机状态数据');
        return;
    }

    // console.log('更新电机状态:', data); // 注释掉以提高性能

    // 在更新latestMotorData之前检查状态变化
    const enableStatusChanged = !latestMotorData || latestMotorData.is_enabled !== data.is_enabled;
    const controlModeChanged = !latestMotorData || latestMotorData.control_mode !== data.control_mode;

    // 更新latestMotorData
    latestMotorData = data;

    // 更新UI显示
    const totalRoundsElement = document.getElementById('totalRounds');
    if (totalRoundsElement) totalRoundsElement.textContent = `${(data.total_rounds || 0).toFixed(2)} rounds`;

    const angleElement = document.getElementById('angle');
    if (angleElement) angleElement.textContent = (data.angle || 0).toFixed(2) + ' m';

    const speedElement = document.getElementById('speed');
    if (speedElement) speedElement.textContent = (data.speed || 0).toFixed(2) + ' rad/s';

    const torqueElement = document.getElementById('torque');
    if (torqueElement) torqueElement.textContent = (data.torque || 0).toFixed(2) + ' N·m';

    const temperatureElement = document.getElementById('temperature');
    if (temperatureElement) temperatureElement.textContent = (data.temperature || 0).toFixed(1) + ' °C';



    // 只在状态真正改变时更新UI，避免不必要的DOM操作
    if (enableStatusChanged) {
        updateEnableStatus(data.is_enabled);
        console.log('使能状态更新:', data.is_enabled); // 添加调试输出
    }

    // 只在控制模式改变时更新
    if (controlModeChanged) {
        updateControlPanel(data.control_mode || 'pc');
    }

    // 更新急停状态
    if (data.emergency_stop !== undefined && data.emergency_stop !== emergencyStopActive) {
        emergencyStopActive = data.emergency_stop;
        updateEmergencyStopUI(emergencyStopActive);
        console.log('急停状态更新:', emergencyStopActive); // 添加调试输出
    }

    // 急停状态更新频率较低，保持原有逻辑
    updateControlsBasedOnEmergencyStop();
}

// 更新网络状态 - 已禁用显示
function updateNetworkStatus(data) {
    // 网络状态显示已被移除，此函数保留以避免错误
    return;
}

// 全局变量跟踪MAVLink连接状态
let lastMavlinkConnected = null;

// 更新MAVLink连接状态
function updateMavlinkStatus(data) {
    if (!data) return;

    const isConnected = data.connected && !data.connection_lost;

    // 只在连接状态发生变化时显示Toast消息
    if (lastMavlinkConnected !== null && lastMavlinkConnected !== isConnected) {
        if (!isConnected) {
            // MAVLink断连时显示警告消息
            showToast(
                currentLang === 'zh-cn' ? 'MAVLink连接断开' : 'MAVLink disconnected',
                'warning',
                5000,
                'top-left'
            );
        } else {
            // MAVLink重连时显示成功消息
            showToast(
                currentLang === 'zh-cn' ? 'MAVLink连接已恢复' : 'MAVLink reconnected',
                'success',
                3000,
                'top-left'
            );
        }
    }

    // 更新状态记录
    lastMavlinkConnected = isConnected;
}

// 更新SBUS数据 - 只处理WebSocket数据，不进行HTTP轮询
function updateSbusData(data) {
    if (!data) return;

    // 更新SBUS信号状态
    const sbusAlert = document.getElementById('sbusSignalAlert');
    sbusAlert.style.display = data.signal_detected ? 'none' : 'block';

    // 更新按钮状态 - 已移除开始校准和重置校准按钮

    // 更新故障保护状态
    updateFailSafeStatus(data.failSafe);

    // 更新丢帧状态
    updateLostFrameStatus(data.lostFrame);

    // 更新通道显示
    if (data.channels && Array.isArray(data.channels)) {
        updateChannelsDisplay(data.channels, data.calibration, data.signal_detected);
    }
}

// 更新故障保护状态
function updateFailSafeStatus(isFailSafe) {
    const failSafe = document.getElementById('failSafe');
    const failSafeText = document.getElementById('failSafeText');

    if (isFailSafe) {
        failSafe.classList.remove('status-ok');
        failSafe.classList.add('status-error');
        failSafeText.textContent = currentLang === 'zh-cn' ? '故障保护已触发' : 'Failsafe Active';
    } else {
        failSafe.classList.remove('status-error');
        failSafe.classList.add('status-ok');
        failSafeText.textContent = currentLang === 'zh-cn' ? '故障保护正常' : 'Failsafe OK';
    }
}

// 更新丢帧状态
function updateLostFrameStatus(isLostFrame) {
    const lostFrame = document.getElementById('lostFrame');
    const lostFrameText = document.getElementById('lostFrameText');

    if (isLostFrame) {
        lostFrame.classList.remove('status-ok');
        lostFrame.classList.add('status-error');
        lostFrameText.textContent = currentLang === 'zh-cn' ? '遥控器丢帧' : 'Lost Frame';
    } else {
        lostFrame.classList.remove('status-error');
        lostFrame.classList.add('status-ok');
        lostFrameText.textContent = currentLang === 'zh-cn' ? '遥控器正常' : 'Frame OK';
    }
}

// 更新通道显示
function updateChannelsDisplay(channels, calibration, signalDetected) {
    if (!channels || !Array.isArray(channels)) return;

    channels.forEach((channelValue, index) => {
        if (index >= 16) return; // 只处理前16个通道

        // 获取DOM元素
        const channelElement = document.getElementById(`channel${index}`);
        if (!channelElement) return;

        const progressBar = channelElement.parentElement;
        const range = document.getElementById(`range${index}`);
        const channelBar = progressBar.parentElement;
        const valueElement = document.getElementById(`value${index}`);
        const rawElement = document.getElementById(`raw${index}`);
        const limitsElement = document.getElementById(`limits${index}`);

        if (signalDetected) {
            // 确保channelValue不是undefined，并使用默认值1500
            const value = typeof channelValue !== 'undefined' ? channelValue : 1500;
            // 计算当前值在1000-2000范围内的位置
            const percentage = Math.max(0, Math.min(100, ((value - 1000) / (2000 - 1000)) * 100));

            // 更新当前值标记位置
            const currentMarker = document.getElementById(`current${index}`);
            if (currentMarker) {
                currentMarker.style.left = `${percentage}%`;
            }

            // 更新进度条
            channelElement.style.width = `${percentage}%`;

            // 更新标准化值显示
            if (valueElement) {
                valueElement.textContent = value;
            }

            // 更新原始值显示
            if (rawElement) {
                rawElement.textContent = `${languages[currentLang].current}: ${value}`;
            }

            // 校准功能已移除 - 不再显示校准相关信息
        } else {
            // 无信号时清空显示
            channelElement.style.width = '0%';
            if (valueElement) valueElement.textContent = '0';
            if (rawElement) rawElement.textContent = '';
            if (range) range.style.display = 'none';
            if (channelBar) channelBar.classList.remove('calibrating');
        }
    });
}



// 更新急停状态
function updateEmergencyStopStatus(data) {
    emergencyStopActive = data.isActive;
    updateEmergencyStopUI(emergencyStopActive);
    updateControlsBasedOnEmergencyStop();
}

// 处理配置更新
function handleConfigUpdate(data) {
    calibrationData = data.calibration || {};
    maxConfigSpeed = data.max_speed || 25.0; // 更新最大速度

    document.getElementById('enableChannel').value = data.enable_channel;
    document.getElementById('controlChannel').value = data.control_channel;
    document.getElementById('maxSpeed').value = data.max_speed;

    // 更新滑块的最大值和最小值
    const speedSlider = document.getElementById('speedSlider');
    speedSlider.min = -maxConfigSpeed;
    speedSlider.max = maxConfigSpeed;

    updateSliderSpeed(); // 更新滑块显示
    updateSpeedControls(); // 更新速度输入框的placeholder

    // 更新SBUS通道校准UI
    updateSbusChannelsCalibrationUI(calibrationData);
}

// 处理配置保存
function handleConfigSave(currentConfig, updates) {
    // 合并当前配置和更新的字段
    const newConfig = {
        ...currentConfig,
        ...updates
    };

    // 发送完整配置进行保存
    sendWebSocketCommand('set_config', null, newConfig);
}

// 确保 updateControlPanel 函数存在且正确处理 'control_mode'
function updateControlPanel(mode) {
    const controlModeSelect = document.getElementById('controlMode');
    if (controlModeSelect) {
        controlModeSelect.value = mode;
    }

    // 根据控制模式启用/禁用控制按钮
    updateControlButtonsState(mode);
}

// 根据控制模式更新控制按钮状态
function updateControlButtonsState(mode) {
    const enableButton = document.getElementById('enableButton');
    const disableButton = document.getElementById('disableButton');
    const setSpeedButton = document.getElementById('setSpeedButton');
    const speedInput = document.getElementById('speedInput');
    const speedSlider = document.getElementById('speedSlider');

    const isRemoteMode = (mode === 'remote');

    // 在遥控器模式下禁用控制按钮
    if (enableButton) {
        enableButton.disabled = isRemoteMode;
        enableButton.style.opacity = isRemoteMode ? '0.5' : '1';
    }
    if (disableButton) {
        disableButton.disabled = isRemoteMode;
        disableButton.style.opacity = isRemoteMode ? '0.5' : '1';
    }
    if (setSpeedButton) {
        setSpeedButton.disabled = isRemoteMode;
        setSpeedButton.style.opacity = isRemoteMode ? '0.5' : '1';
    }
    if (speedInput) {
        speedInput.disabled = isRemoteMode;
        speedInput.style.opacity = isRemoteMode ? '0.5' : '1';
    }
    if (speedSlider) {
        speedSlider.disabled = isRemoteMode;
        speedSlider.style.opacity = isRemoteMode ? '0.5' : '1';
    }
}

// 确保 updateEnableStatus 函数存在且正确处理 'is_enabled'
function updateEnableStatus(isEnabled) {
    const enableStatusElement = document.getElementById('enableStatus');
    if (enableStatusElement) {
        if (isEnabled) {
            enableStatusElement.textContent = currentLang === 'zh-cn' ? '已使能' : 'Enabled';
            enableStatusElement.className = 'status-indicator enabled';
        } else {
            enableStatusElement.textContent = currentLang === 'zh-cn' ? '已失能' : 'Disabled';
            enableStatusElement.className = 'status-indicator disabled';
        }
    }
}

// 确保 updateEmergencyStopUI 函数存在
function updateEmergencyStopUI(isActive) {
    const emergencyStopStatusElement = document.getElementById('emergencyStopStatus');
    if (emergencyStopStatusElement) {
        if (isActive) {
            emergencyStopStatusElement.textContent = currentLang === 'zh-cn' ? '急停激活' : 'Emergency Stop Active';
            emergencyStopStatusElement.classList.remove('status-ok');
            emergencyStopStatusElement.classList.add('status-error');
        } else {
            emergencyStopStatusElement.textContent = currentLang === 'zh-cn' ? '急停未激活' : 'Emergency Stop Inactive';
            emergencyStopStatusElement.classList.remove('status-error');
            emergencyStopStatusElement.classList.add('status-ok');
        }
    }
}

// 确保 updateControlsBasedOnEmergencyStop 函数存在
function updateControlsBasedOnEmergencyStop() {
    const enableButton = document.getElementById('enableButton');
    const disableButton = document.getElementById('disableButton');
    const setSpeedButton = document.getElementById('setSpeedButton');
    const speedInput = document.getElementById('speedInput');
    const speedSlider = document.getElementById('speedSlider');
    const setControlModeButton = document.getElementById('setControlMode');
    const controlModeSelect = document.getElementById('controlMode');

    if (emergencyStopActive) {
        // 急停激活时，滑块显示完整范围但只允许正速度（放缆）
        if (setSpeedButton) setSpeedButton.disabled = false;
        if (speedInput) speedInput.disabled = false;
        if (speedSlider) {
            speedSlider.disabled = false;
            speedSlider.min = -maxConfigSpeed; // 显示完整范围
            speedSlider.max = maxConfigSpeed;
            // 急停状态下允许拖动到负值，只在松手时回弹
        }
        // 急停时保持所有控制功能可用，只限制速度方向
        if (enableButton) enableButton.disabled = false;
        if (disableButton) disableButton.disabled = false;
        if (setControlModeButton) setControlModeButton.disabled = false;
        if (controlModeSelect) controlModeSelect.disabled = false;

    } else {
        // 急停未激活，恢复正常控制
        if (enableButton) enableButton.disabled = false;
        if (disableButton) disableButton.disabled = false;
        if (setSpeedButton) setSpeedButton.disabled = false;
        if (speedInput) speedInput.disabled = false;
        if (speedSlider) {
            speedSlider.disabled = false;
            // 恢复滑块的完整范围
            speedSlider.min = -maxConfigSpeed;
            speedSlider.max = maxConfigSpeed;
        }
        if (setControlModeButton) setControlModeButton.disabled = false;
        if (controlModeSelect) controlModeSelect.disabled = false;
    }
}

// 确保 updateSbusChannelsCalibrationUI 存在
function updateSbusChannelsCalibrationUI(calibrationData) {
    // 如果没有校准数据，则初始化为默认值
    if (Object.keys(calibrationData).length === 0) {
        for (let i = 1; i <= 16; i++) {
            calibrationData[`channel${i}`] = { min: 65535, max: 0 };
        }
    }

    const channelsContainer = document.getElementById('channelsContainer');
    channelsContainer.innerHTML = ''; // 清空现有内容

    for (let i = 1; i <= 16; i++) {
        const channelDiv = document.createElement('div');
        channelDiv.className = 'channel-bar';
        channelDiv.innerHTML = `
            <span class="channel-label">${languages[currentLang].channel} ${i}</span>
            <div class="progress-bar">
                <div class="calibration-range" id="range${i - 1}"></div>
                <div class="progress-fill" id="channel${i - 1}"></div>
                <div class="current-marker" id="current${i - 1}"></div>
            </div>
            <span class="channel-value" id="value${i - 1}">0</span>
            <span class="current-raw" id="raw${i - 1}"></span>
            <span class="channel-limits" id="limits${i - 1}"></span>
        `;
        channelsContainer.appendChild(channelDiv);
    }
}

// 节流速度更新 - 仅用于滑块连续操作
function throttleSpeedUpdate(speed) {
    if (speedUpdateTimeout) {
        clearTimeout(speedUpdateTimeout);
    }
    speedUpdateTimeout = setTimeout(() => {
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc' && latestMotorData.is_enabled) {
            // 急停状态下只允许正速度（放缆），阻止负速度（收缆）
            if (emergencyStopActive && speed < 0) {
                speed = 0;
                console.log('急停状态下阻止负速度（收缆），只允许正速度（放缆）');
            }
            sendWebSocketCommand('set_speed', speed);
            lastSetSpeed = speed;
        } else {
            // 如果不是PC模式或电机未使能，则将速度设置为0
            sendWebSocketCommand('set_speed', 0);
            lastSetSpeed = 0;
        }
    }, SPEED_UPDATE_DELAY);
}

// 直接速度更新 - 用于按钮点击等即时操作
function immediateSpeedUpdate(speed) {
    const controlMode = document.getElementById('controlMode').value;
    if (controlMode === 'pc' && latestMotorData.is_enabled) {
        // 急停状态下只允许正速度（放缆），阻止负速度（收缆）
        if (emergencyStopActive && speed < 0) {
            speed = 0;
            console.log('急停状态下阻止负速度（收缆），只允许正速度（放缆）');
        }
        sendWebSocketCommand('set_speed', speed);
        lastSetSpeed = speed;
    } else {
        // 如果不是PC模式或电机未使能，则将速度设置为0
        sendWebSocketCommand('set_speed', 0);
        lastSetSpeed = 0;
    }
}

// 确保 updateSliderSpeed 存在
function updateSliderSpeed() {
    const speedSlider = document.getElementById('speedSlider');
    const sliderValue = parseFloat(speedSlider.value);
    document.getElementById('slider-speed').textContent = `${languages[currentLang].currentSliderSpeed} ${sliderValue.toFixed(2)} rad/s`;
}

// 语言切换函数
const languages = {
    'zh-cn': {
        winchControl: '绞车控制栏',
        winchStatus: '绞车当前状态',
        enableWinch: '使能绞车',
        disableWinch: '失能绞车',
        speed: '速度',
        setSpeed: '设置速度',
        pcControl: 'web界面控制',
        remoteControl: '遥控器控制',
        setControlMode: '设置控制模式(WEB/遥控器)',
        currentSliderSpeed: '当前滑块设置速度:',
        noteSliderSpeed: '注：滑块速度范围受系统配置中的【绞车最大转速】限制',
        emergencyStopInactive: '急停未激活',
        emergencyStopActive: '急停激活',
        isEnabled: '是否使能:',
        enabled: '已使能',
        disabled: '已失能',
        totalRounds: '总圈数:',
        currentCableLength: '当前放线长度:',
        torque: '扭矩:',
        temperature: '温度:',
        systemConfig: '系统配置',
        enableChannel: '绞车使能通道',
        controlChannel: '收放缆控制通道',
        maxWinchSpeed: '绞车最大转速 (rad/s)',
        loadConfig: '读取配置',
        saveConfig: '保存配置',
        sbusData: 'SBUS数据',
        sbusSignalLost: '⚠️ 未检测到SBUS信号，请检查接收机连接',
        // 新增翻译
        title: 'FT120 绞车控制面板',
        companyName: '翱飞(无锡)科技有限公司',
        navControlMonitor: '绞车控制数据主页',
        navConfig: '系统配置',
        navChannelData: '通道数据',
        channelData: '通道数据',
        cancel: '取消',
        confirm: '确认',
        speedPlaceholder: '速度 (rad/s)',
        currentSpeed: '当前速度:',
        logoLoadFailed: 'Logo加载失败',
        channelSignalLost: '⚠️ 未检测到通道信号，请检查连接',
        failsafeStatus: '故障保护状态',
        lostFrameStatus: '丢帧状态',
        // Toast消息
        wsConnected: 'WebSocket连接已建立',
        wsDisconnected: 'WebSocket连接断开，正在重连...',
        wsConnectionFailed: 'WebSocket连接失败，请刷新页面重试',
        wsConnectionError: 'WebSocket连接错误',
        wsNotConnected: 'WebSocket未连接',
        configLoadSuccess: '配置读取成功',
        configSaveSuccess: '配置保存成功',
        emergencyStopNegativeSpeed: '急停状态下无法收揽',
        invalidSpeed: '请输入有效速度',
        wsNotConnectedConfig: 'WebSocket未连接，无法读取配置',
        wsNotConnectedSave: 'WebSocket未连接，无法保存配置',
        enableChannelRange: '绞车使能通道必须在1-16之间',
        controlChannelRange: '收放缆控制通道必须在1-16之间',
        maxSpeedRange: '绞车最大转速必须在0.1-45之间',
        channel: '通道',
        current: '当前值',

        connectionType: '连接类型:',
        ipAddress: 'IP地址:',
        networkEthernet: '以太网连接',
        networkWifi: '无线连接',
        networkNone: '无网络连接',
        calibrationError: '校准数据无效',
        commandSuccess: '命令发送成功',
        commandFailed: '命令发送失败:',
        networkError: '网络错误:',
        webSocketConnected: 'WebSocket连接已建立',
        webSocketDisconnected: 'WebSocket连接断开，正在重连...',
        webSocketFailed: 'WebSocket连接失败，请刷新页面重试',
        webSocketError: 'WebSocket连接错误',
        webSocketNotConnected: 'WebSocket未连接',
        invalidSpeed: '请输入有效速度',
        channel: '通道',
        current: '当前',
        min: '最小',
        max: '最大',
        failsafeActive: '故障保护已触发',
        failsafeOK: '故障保护正常',
        lostFrame: '遥控器丢帧',
        frameOK: '遥控器正常',
        configInvalid: '配置无效'
    },
    'en': {
        winchControl: 'Winch Control',
        winchStatus: 'Winch Status',
        enableWinch: 'Enable Winch',
        disableWinch: 'Disable Winch',
        speed: 'Speed',
        setSpeed: 'Set Speed',
        pcControl: 'Web Interface Control',
        remoteControl: 'Remote Control',
        setControlMode: 'Set Control Mode (WEB/Remote)',
        currentSliderSpeed: 'Current Slider Speed:',
        noteSliderSpeed: 'Note: Slider speed range is limited by "Max Winch Speed" in System Configuration',
        emergencyStopInactive: 'Emergency Stop Inactive',
        emergencyStopActive: 'Emergency Stop Active',
        isEnabled: 'Is Enabled:',
        enabled: 'Enabled',
        disabled: 'Disabled',
        totalRounds: 'Total Rounds:',
        currentCableLength: 'Current Cable Length:',
        torque: 'Torque:',
        temperature: 'Temperature:',
        systemConfig: 'System Configuration',
        // 新增翻译
        title: 'FT120 Winch Control Panel',
        companyName: 'Ocean Physics Technology',
        navControlMonitor: 'Winch Control & Data',
        navConfig: 'System Configuration',
        navChannelData: 'Channel Data',
        channelData: 'Channel Data',
        cancel: 'Cancel',
        confirm: 'Confirm',
        speedPlaceholder: 'Speed (rad/s)',
        currentSpeed: 'Current Speed:',
        logoLoadFailed: 'Logo Load Failed',
        channelSignalLost: '⚠️ No channel signal detected, please check connection',
        failsafeStatus: 'Failsafe Status',
        lostFrameStatus: 'Lost Frame Status',
        // Toast消息
        wsConnected: 'WebSocket connected',
        wsDisconnected: 'WebSocket disconnected, reconnecting...',
        wsConnectionFailed: 'WebSocket connection failed, please refresh the page',
        wsConnectionError: 'WebSocket connection error',
        wsNotConnected: 'WebSocket not connected',
        configLoadSuccess: 'Configuration loaded successfully',
        configSaveSuccess: 'Configuration saved successfully',
        emergencyStopNegativeSpeed: 'Emergency stop: negative speed converted to 0 (stop)',
        invalidSpeed: 'Please enter a valid speed',
        wsNotConnectedConfig: 'WebSocket not connected, cannot load configuration',
        wsNotConnectedSave: 'WebSocket not connected, cannot save configuration',
        enableChannelRange: 'Winch enable channel must be between 1-16',
        controlChannelRange: 'Winch control channel must be between 1-16',
        maxSpeedRange: 'Max winch speed must be between 0.1-45',
        channel: 'Channel',
        current: 'Current',
        enableChannel: 'Winch Enable Channel',
        controlChannel: 'Winch Control Channel',
        maxWinchSpeed: 'Max Winch Speed (rad/s)',
        loadConfig: 'Load Configuration',
        saveConfig: 'Save Configuration',
        sbusData: 'SBUS Data',
        sbusSignalLost: '⚠️ SBUS signal not detected, please check receiver connection',

        connectionType: 'Connection Type:',
        ipAddress: 'IP Address:',
        networkEthernet: 'Ethernet Connected',
        networkWifi: 'WiFi Connected',
        networkNone: 'No Network Connection',
        calibrationError: 'Invalid Calibration Data',
        commandSuccess: 'Command sent successfully',
        commandFailed: 'Command failed:',
        networkError: 'Network error:',
        webSocketConnected: 'WebSocket Connected',
        webSocketDisconnected: 'WebSocket disconnected, reconnecting...',
        webSocketFailed: 'WebSocket connection failed, please refresh the page',
        webSocketError: 'WebSocket connection error',
        webSocketNotConnected: 'WebSocket not connected',
        invalidSpeed: 'Please enter a valid speed',
        channel: 'Channel',
        current: 'Current',
        min: 'Min',
        max: 'Max',
        failsafeActive: 'Failsafe Active',
        failsafeOK: 'Failsafe OK',
        lostFrame: 'Lost Frame',
        frameOK: 'Frame OK',
        configInvalid: 'Configuration Invalid'
    }
};

function switchLanguage() {
    currentLang = currentLang === 'zh-cn' ? 'en' : 'zh-cn';
    updateLanguage();
    localStorage.setItem('lang', currentLang);
}

// 侧边栏切换功能
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');

    sidebar.classList.toggle('collapsed');
    sidebarToggle.classList.toggle('collapsed');

    // CSS中的 .sidebar.collapsed~.page 规则会自动调整页面布局
}

function updateLanguage() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (languages[currentLang][key]) {
            element.textContent = languages[currentLang][key];
        }
    });

    // 处理placeholder属性
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        if (languages[currentLang][key]) {
            element.placeholder = languages[currentLang][key];
        }
    });

    // 更新页面标题
    document.title = languages[currentLang].title;

    // 更新主标题
    const h1Element = document.querySelector('h1');
    if (h1Element) {
        h1Element.textContent = languages[currentLang].title;
    }

    // 更新公司名称
    const companyLogo = document.querySelector('.company-logo a');
    if (companyLogo) {
        companyLogo.textContent = languages[currentLang].companyName;
    }

    // 更新导航按钮
    const navButtons = document.querySelectorAll('.nav-btn');
    if (navButtons.length >= 3) {
        navButtons[0].textContent = languages[currentLang].navControlMonitor;
        navButtons[1].textContent = languages[currentLang].navConfig;
        navButtons[2].textContent = languages[currentLang].navChannelData;
    }

    // 更新页面标题
    const configPageH2 = document.querySelector('#config-page h2');
    if (configPageH2) {
        configPageH2.textContent = languages[currentLang].systemConfig;
    }

    const sbusPageH2 = document.querySelector('#sbus-page h2');
    if (sbusPageH2) {
        sbusPageH2.textContent = languages[currentLang].channelData;
    }

    // 更新确认对话框按钮
    const cancelButton = document.getElementById('cancelButton');
    const confirmButton = document.getElementById('confirmButton');
    if (cancelButton) cancelButton.textContent = languages[currentLang].cancel;
    if (confirmButton) confirmButton.textContent = languages[currentLang].confirm;

    // 更新所有文本内容的元素
    document.getElementById('enableButton').textContent = languages[currentLang].enableWinch;
    document.getElementById('disableButton').textContent = languages[currentLang].disableWinch;
    document.getElementById('setSpeedButton').textContent = languages[currentLang].setSpeed;
    document.getElementById('setControlMode').textContent = languages[currentLang].setControlMode;
    document.getElementById('loadConfig').textContent = languages[currentLang].loadConfig;
    document.getElementById('saveConfig').textContent = languages[currentLang].saveConfig;

    document.getElementById('langToggle').textContent = currentLang === 'zh-cn' ? 'Switch to English' : '切换到中文';

    document.querySelector('#controlMode option[value="pc"]').textContent = languages[currentLang].pcControl;
    document.querySelector('#controlMode option[value="remote"]').textContent = languages[currentLang].remoteControl;

    // 更新输入框placeholder
    const speedInput = document.getElementById('speedInput');
    if (speedInput) {
        speedInput.placeholder = languages[currentLang].speedPlaceholder;
    }

    // 更新状态文本
    const failSafeText = document.getElementById('failSafeText');
    const lostFrameText = document.getElementById('lostFrameText');
    if (failSafeText) failSafeText.textContent = languages[currentLang].failsafeStatus;
    if (lostFrameText) lostFrameText.textContent = languages[currentLang].lostFrameStatus;

    // 更新通道信号丢失提示
    const sbusSignalAlert = document.querySelector('#sbusSignalAlert p');
    if (sbusSignalAlert) {
        sbusSignalAlert.textContent = languages[currentLang].channelSignalLost;
    }

    // 更新已有的动态文本
    updateSliderSpeed();
    updateEnableStatus(latestMotorData.is_enabled); // 重新渲染使能状态
    updateEmergencyStopUI(emergencyStopActive); // 重新渲染急停状态

    // 重新生成通道UI以更新语言
    updateSbusChannelsCalibrationUI(calibrationData);
}

// 确保 initSbusChannels 存在，并且只负责创建UI元素，不拉取数据
function initSbusChannels() {
    updateSbusChannelsCalibrationUI(calibrationData); // 初始化显示空校准数据
}

// 确保 initToastAndDialog 存在
function initToastAndDialog() {
    const toastMessage = document.getElementById('toastMessage');
    toastMessage.addEventListener('click', () => {
        toastMessage.classList.remove('show');
    });

    const confirmButton = document.getElementById('confirmButton');
    const cancelButton = document.getElementById('cancelButton');

    confirmButton.addEventListener('click', () => {
        hideConfirmDialog();
    });

    cancelButton.addEventListener('click', () => {
        hideConfirmDialog();
    });
}

// 确保 showToast 存在
function showToast(message, type = 'info', duration = 3000, position = 'center') {
    const toastMessage = document.getElementById('toastMessage');
    if (toastMessage) {
        // 清除之前的类
        toastMessage.className = 'toast';
        toastMessage.textContent = message;

        // 强制重绘
        toastMessage.offsetHeight;

        // 添加新的类
        toastMessage.className = `toast show ${type}`;

        // 确保样式被应用
        toastMessage.style.display = 'block';
        toastMessage.style.opacity = '1';
        toastMessage.style.zIndex = '9999';
        toastMessage.style.position = 'fixed';

        // 根据位置参数设置不同的显示位置
        if (position === 'top-left') {
            toastMessage.style.top = '20px';
            toastMessage.style.left = '20px';
            toastMessage.style.transform = 'none';
        } else {
            // 默认居中显示
            toastMessage.style.top = '50%';
            toastMessage.style.left = '50%';
            toastMessage.style.transform = 'translate(-50%, -50%)';
        }

        setTimeout(() => {
            toastMessage.classList.remove('show');
            toastMessage.style.opacity = '0';
        }, duration);
    }
}

// 确保 showConfirmDialog 存在
let confirmCallback = null; // 用于存储确认回调函数
function showConfirmDialog(message, callback) {
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmMessage = document.getElementById('confirmMessage');
    if (confirmDialog && confirmMessage) {
        confirmMessage.textContent = message;
        confirmDialog.style.display = 'flex';
        confirmCallback = callback; // 存储回调函数
    }
}

// 确保 hideConfirmDialog 存在
function hideConfirmDialog() {
    const confirmDialog = document.getElementById('confirmDialog');
    if (confirmDialog) {
        confirmDialog.style.display = 'none';
    }
}

// 确保 showLoading 存在
function showLoading() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }
}

// 确保 hideLoading 存在
function hideLoading() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'none';
    }
}

// 在 DOMContentLoaded 中添加 init 函数调用
document.addEventListener('DOMContentLoaded', () => {
    // 初始化 WebSocket 连接
    connectWebSocket();

    // 初始化 Toast 和 Confirm Dialog
    initToastAndDialog();

    // 初始化 SBUS 通道 UI (仅UI，不拉取数据)
    initSbusChannels();

    // 更新语言
    updateLanguage();

    // 初始显示页面
    showPage('control_monitor');

    // 初始化控制按钮状态（默认为遥控器模式）
    updateControlButtonsState('remote');

    // 控制按钮事件监听器
    document.getElementById('enableButton').addEventListener('click', () => {
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            sendWebSocketCommand('enable');
        }
    });

    document.getElementById('disableButton').addEventListener('click', () => {
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            sendWebSocketCommand('disable');
        }
    });

    document.getElementById('setSpeedButton').addEventListener('click', () => {
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            let speedInput = parseFloat(document.getElementById('speedInput').value);
            if (!isNaN(speedInput)) {
                // 检查速度是否超过最大限制
                if (Math.abs(speedInput) > maxConfigSpeed) {
                    showToast(
                        currentLang === 'zh-cn'
                            ? `输入速度超限！最大速度为 ${maxConfigSpeed} rad/s`
                            : `Speed exceeds limit! Maximum speed is ${maxConfigSpeed} rad/s`,
                        'warning'
                    );
                    return; // 阻止设置超限速度
                }

                // 急停状态下，如果用户试图设置负速度，将速度设置为0
                if (emergencyStopActive && speedInput < 0) {
                    speedInput = 0;
                    showToast(languages[currentLang].emergencyStopNegativeSpeed, 'warning');
                }
                immediateSpeedUpdate(speedInput); // 使用即时更新，无延迟
            } else {
                showToast(languages[currentLang].invalidSpeed, 'error');
            }
        }
    });

    document.getElementById('setControlMode').addEventListener('click', () => {
        const mode = document.getElementById('controlMode').value;
        sendWebSocketCommand('set_mode', mode);
    });

    const speedSlider = document.getElementById('speedSlider');
    speedSlider.addEventListener('input', () => {
        const sliderValue = parseFloat(speedSlider.value);
        document.getElementById('slider-speed').textContent = currentLang === 'zh-cn' ? `当前滑块设置速度: ${sliderValue.toFixed(2)} rad/s` : `Current Slider Speed: ${sliderValue.toFixed(2)} rad/s`;

        // 一旦使用滑块，就清空速度输入框
        document.getElementById('speedInput').value = '';

        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            throttleSpeedUpdate(sliderValue);
        }
    });

    // 添加鼠标释放事件监听器，实现滑块自动归零
    speedSlider.addEventListener('mouseup', () => {
        // 清除任何待执行的节流速度更新，防止延迟命令在归零后执行
        if (speedUpdateTimeout) {
            clearTimeout(speedUpdateTimeout);
            speedUpdateTimeout = null;
        }

        // 急停状态下，如果滑块在负值位置，强制回弹到0
        if (emergencyStopActive && speedSlider.value < 0) {
            speedSlider.value = 0;
        } else {
            // 正常情况下滑块归零
            speedSlider.value = 0;
        }

        // 速度输入框也归零
        document.getElementById('speedInput').value = '';
        // 更新显示文本
        document.getElementById('slider-speed').textContent = currentLang === 'zh-cn' ? `当前滑块设置速度: 0.00 rad/s` : `Current Slider Speed: 0.00 rad/s`;

        // 发送归零速度命令
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            immediateSpeedUpdate(0); // 立即发送归零命令
        }
    });

    // 添加触摸结束事件监听器，支持移动设备
    speedSlider.addEventListener('touchend', () => {
        // 清除任何待执行的节流速度更新，防止延迟命令在归零后执行
        if (speedUpdateTimeout) {
            clearTimeout(speedUpdateTimeout);
            speedUpdateTimeout = null;
        }

        // 急停状态下，如果滑块在负值位置，强制回弹到0
        if (emergencyStopActive && speedSlider.value < 0) {
            speedSlider.value = 0;
        } else {
            // 正常情况下滑块归零
            speedSlider.value = 0;
        }

        // 速度输入框也归零
        document.getElementById('speedInput').value = '';
        // 更新显示文本
        document.getElementById('slider-speed').textContent = currentLang === 'zh-cn' ? `当前滑块设置速度: 0.00 rad/s` : `Current Slider Speed: 0.00 rad/s`;

        // 发送归零速度命令
        const controlMode = document.getElementById('controlMode').value;
        if (controlMode === 'pc') {
            immediateSpeedUpdate(0); // 立即发送归零命令
        }
    });

    // 配置页面事件监听器
    document.getElementById('loadConfig').addEventListener('click', () => {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            showToast(languages[currentLang].wsNotConnectedConfig, 'error', 3000, 'top-left');
            return;
        }
        isManualConfigRequest = true; // 设置手动请求标志
        sendWebSocketCommand('get_config'); // 通过WebSocket获取配置
    });

    document.getElementById('saveConfig').addEventListener('click', () => {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            showToast(languages[currentLang].wsNotConnectedSave, 'error', 3000, 'top-left');
            return;
        }

        const enableChannel = parseInt(document.getElementById('enableChannel').value);
        const controlChannel = parseInt(document.getElementById('controlChannel').value);
        const maxSpeed = parseFloat(document.getElementById('maxSpeed').value);

        // 验证输入值
        if (isNaN(enableChannel) || enableChannel < 1 || enableChannel > 16) {
            showToast(languages[currentLang].enableChannelRange, 'error');
            return;
        }
        if (isNaN(controlChannel) || controlChannel < 1 || controlChannel > 16) {
            showToast(languages[currentLang].controlChannelRange, 'error');
            return;
        }
        if (isNaN(maxSpeed) || maxSpeed <= 0 || maxSpeed > 45) {
            showToast(languages[currentLang].maxSpeedRange, 'error');
            return;
        }

        // 获取当前完整配置，只更新界面上的字段
        sendWebSocketCommand('get_config_for_save', null, {
            enable_channel: enableChannel,
            control_channel: controlChannel,
            max_speed: maxSpeed
        });
    });

    // 语言切换
    document.getElementById('langToggle').addEventListener('click', switchLanguage);

    // 侧边栏切换
    document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
});

// 辅助函数：更新速度输入框的placeholder
function updateSpeedControls() {
    const maxSpeedInput = document.getElementById('maxSpeed');
    const speedInput = document.getElementById('speedInput');
    if (maxSpeedInput && speedInput) {
        const speedText = currentLang === 'zh-cn' ? '速度' : 'Speed';
        speedInput.placeholder = `${speedText} (0 ~ ${maxSpeedInput.value} rad/s)`;
    }
}
