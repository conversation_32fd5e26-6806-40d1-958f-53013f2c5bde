"""
配置管理模块
处理配置文件的加载、保存、验证和缓存
"""

import os
import logging
import tomli
import tomli_w

# 配置日志
logger = logging.getLogger(__name__)

# 配置文件路径
CONFIG_FILE = "config.toml"

# 全局配置缓存，避免频繁读取文件
cached_config = None
config_last_modified = 0

# 配置数据结构
default_config = {
    "enable_channel": 8,
    "control_channel": 1,
    "max_speed": 10,
    "initial_max_speed": 5,
    "limit_rounds": 10,
    "cable_parameters": {
        "inner_diameter": 0.044,  # 绞盘内径 (米)
        "cable_diameter": 0.0018,  # 缆绳直径 (米)
        "cylinder_length": 0.0525,  # 盘缆筒长度 (米)
        "total_cable_length": 100.0,  # 总缆绳长度 (米)
    },
}


def validate_config(config: dict) -> bool:
    """验证配置是否有效"""
    try:
        # 验证基本参数
        if not (1 <= config["enable_channel"] <= 16):
            return False
        if not (1 <= config["control_channel"] <= 16):
            return False
        if not (0 < config["max_speed"] <= 45.0):  # 使用电机最大速度限制
            return False
        if not (0 < config["initial_max_speed"] <= 45.0):
            return False
        if not (0 < config["limit_rounds"] <= 1000):
            return False

        # 验证缆绳参数
        cable_params = config.get("cable_parameters", {})
        if not (0 < cable_params.get("inner_diameter", 0) <= 1.0):  # 内径应在合理范围内
            return False
        if not (
            0 < cable_params.get("cable_diameter", 0) <= 0.1
        ):  # 缆绳直径应在合理范围内
            return False
        if not (
            0 < cable_params.get("cylinder_length", 0) <= 2.0
        ):  # 盘缆筒长度应在合理范围内
            return False
        if not (
            0 < cable_params.get("total_cable_length", 0) <= 10000.0
        ):  # 总缆绳长度应在合理范围内
            return False

        return True
    except (KeyError, TypeError):
        return False


def load_config() -> dict:
    """加载配置文件，带缓存机制"""
    global cached_config, config_last_modified

    try:
        # 检查文件修改时间
        current_modified = os.path.getmtime(CONFIG_FILE)

        # 如果缓存存在且文件未修改，直接返回缓存
        if cached_config is not None and current_modified == config_last_modified:
            return cached_config

        # 读取配置文件
        with open(CONFIG_FILE, "rb") as f:
            config = tomli.load(f)
            if validate_config(config):
                cached_config = config
                config_last_modified = current_modified
                return config
            logger.warning("配置无效，使用默认配置")
            cached_config = default_config
            return default_config
    except FileNotFoundError:
        logger.info("配置文件不存在，创建默认配置")
        save_config(default_config)
        cached_config = default_config
        config_last_modified = os.path.getmtime(CONFIG_FILE)
        return default_config
    except Exception as e:
        logger.error(f"加载配置时出错: {e}")
        # 如果有缓存，返回缓存；否则返回默认配置
        if cached_config is not None:
            return cached_config
        return default_config


def save_config(config: dict):
    """保存配置到文件"""
    try:
        if not validate_config(config):
            raise ValueError("配置无效")
        with open(CONFIG_FILE, "wb") as f:
            tomli_w.dump(config, f)
        logger.info("配置已保存")
    except Exception as e:
        logger.error(f"保存配置时出错: {e}")
        raise


def get_default_config() -> dict:
    """获取默认配置"""
    return default_config.copy()


def clear_config_cache():
    """清除配置缓存，强制重新加载"""
    global cached_config, config_last_modified
    cached_config = None
    config_last_modified = 0


# 缆绳计算相关功能
PI = 3.14159265359

# 缆绳计算缓存变量
_cable_layers_cache = {}  # 使用字典缓存不同配置的层数计算结果


def calculate_current_cable_length(total_turns, config=None):
    """
    计算当前缆绳长度的函数

    参数:
        total_turns: 绞盘转过的总圈数
        config: 配置字典，如果为None则自动加载

    返回值:
        当前缆绳的长度（单位：米）
    """
    if config is None:
        config = load_config()

    # 获取缆绳参数
    cable_params = config.get("cable_parameters", {})
    inner_diameter = cable_params.get("inner_diameter", 0.044)
    cable_diameter = cable_params.get("cable_diameter", 0.0018)
    cylinder_length = cable_params.get("cylinder_length", 0.0525)
    total_cable_length = cable_params.get("total_cable_length", 100.0)

    # 创建缓存键
    cache_key = (inner_diameter, cable_diameter, cylinder_length, total_cable_length)

    # 1. 计算缆绳缠绕总层数（使用缓存）
    if cache_key not in _cable_layers_cache:
        layers = 0
        remaining_length = total_cable_length
        current_layer_diameter = inner_diameter

        # 计算总层数
        while remaining_length > 0:
            layers += 1
            remaining_length -= (
                PI * current_layer_diameter * (cylinder_length / cable_diameter)
            )
            current_layer_diameter += cable_diameter * 2

        _cable_layers_cache[cache_key] = layers

    cable_layers = _cable_layers_cache[cache_key]

    # 初始化当前已盘绕长度为0
    current_length = 0.0
    # 已用于前面各层计算的电机圈数
    used_turns = 0
    # 当前层数，从最外层开始，所以初始化为总层数
    current_layer = cable_layers

    while current_layer >= 1:
        # 2. 计算当前层的直径
        current_layer_diameter = inner_diameter + current_layer * cable_diameter * 2
        # 3. 计算当前层的周长
        current_layer_circumference = PI * current_layer_diameter
        # 4. 计算当前层可容纳的电机圈数（这里是用盘缆筒长度除以缆绳直径来确定每层可盘绕圈数）
        turns_in_this_layer = int(cylinder_length / cable_diameter)

        # 判断加上当前层的长度后是否超过总长度，如果超过，说明最后一层没盘满，计算最后一层已盘绕的长度
        if (
            current_length + current_layer_circumference * turns_in_this_layer
            > total_cable_length
        ):
            remaining_turns = int(
                (total_cable_length - current_length) / current_layer_circumference
            )
            current_length += remaining_turns * current_layer_circumference
            break

        # 如果电机圈数超过当前层可容纳的圈数，将当前层完整长度累加到总长度
        if total_turns >= used_turns + turns_in_this_layer:
            current_length += current_layer_circumference * turns_in_this_layer
            used_turns += turns_in_this_layer
            current_layer -= 1
        else:
            # 如果电机圈数还在当前层内，计算当前层内已放出的长度并累加到总长度
            current_length += (total_turns - used_turns) * current_layer_circumference
            break

    return current_length


def clear_cable_cache():
    """清除缆绳计算缓存"""
    global _cable_layers_cache
    _cable_layers_cache = {}


def get_cable_parameters(config=None):
    """获取缆绳参数"""
    if config is None:
        config = load_config()
    return config.get("cable_parameters", default_config["cable_parameters"])
