"""
控制处理模块
包含遥控器控制和PC控制的所有逻辑
"""

import logging
import threading
import time

from lib.hardware.mavlink_handler import MavlinkHandler
from lib.hardware.gpio_control import EmergencyStopGPIO

logger = logging.getLogger(__name__)


def value_clamp(value, min_val, max_val):
    """将值限制在指定范围内"""
    return max(min_val, min(value, max_val))


class RemoteControlHandler:
    """遥控器控制处理类"""

    def __init__(self, mavlink_handler: MavlinkHandler, pwm_controller=None):
        self.mavlink_handler = mavlink_handler
        self.pwm_controller = pwm_controller
        self.last_enable = False
        self.last_remote_speed = 0.0
        self.motor_enabled_via_rc = False
        self.last_sent_speed = None
        self.last_connection_state = False
        self.last_status_log_time = 0  # 上次状态日志时间
        self.DEAD_ZONE = 30  # 中位值死区

    def handle_remote_control(
        self, config: dict, emergency_stop: bool, motor_data: dict, current_mode: str
    ) -> float:
        """处理遥控器控制逻辑，返回计算出的速度值"""

        # 检查是否有有效的MAVLink连接和通道数据
        has_valid_connection = self.mavlink_handler.has_valid_connection()
        connection_lost = self.mavlink_handler.is_connection_lost()

        # 连接状态变化检测（避免频繁日志）
        current_time = int(time.time() * 1000)
        if connection_lost != self.last_connection_state:
            # 只有在状态真正稳定变化时才记录日志
            if current_time - self.last_status_log_time > 2000:  # 2秒间隔
                if connection_lost:
                    logger.warning("⚠️ MAVLink连接丢失！遥控器模式下电机速度设为0")
                else:
                    logger.info("✅ MAVLink连接恢复，遥控器控制恢复正常")
                self.last_status_log_time = current_time
            self.last_connection_state = connection_lost

        # 如果连接丢失或没有有效连接，强制返回0速度并持续保持
        if connection_lost or not has_valid_connection:
            self.last_remote_speed = 0.0
            self.motor_enabled_via_rc = False
            # 停止PWM输出
            if self.pwm_controller and self.pwm_controller.is_initialized():
                self.pwm_controller.stop_pwm()
            # 重要：持续返回0速度，确保电机不会通讯丢失
            return 0.0

        # 获取通道数据，连接断开时返回None
        channels = self.mavlink_handler.get_channels()
        if not channels:
            # 没有有效通道数据，返回安全状态
            self.last_remote_speed = 0.0
            self.motor_enabled_via_rc = False
            # 停止PWM输出
            if self.pwm_controller and self.pwm_controller.is_initialized():
                self.pwm_controller.stop_pwm()
            # 重要：持续返回0速度，确保电机不会通讯丢失
            return 0.0

        # 获取通道索引
        enable_ch_index = config.get("enable_channel", 9) - 1
        control_ch_index = config.get("control_channel", 10) - 1

        # 直接将通道数据归一化到 (1000, 2000) 区间，中位值为 1500
        enable_raw = channels[enable_ch_index]
        control_raw = channels[control_ch_index]

        # 使能通道：大于1500为使能
        new_enable = enable_raw > 1500

        # 直接使用遥控器使能状态
        self.motor_enabled_via_rc = new_enable
        if self.motor_enabled_via_rc != self.last_enable:
            self.last_enable = self.motor_enabled_via_rc

        # 控制通道值，中位值为1500
        pwm_ch_value = control_raw

        # 中位值为1500，死区处理
        if abs(pwm_ch_value - 1500) < self.DEAD_ZONE:
            pwm_ch_value = 1500

        max_speed_limit = (
            config.get("initial_max_speed", 10)
            if motor_data["total_rounds"] <= config.get("limit_rounds", 100)
            else config.get("max_speed", 5)
        )

        # 不要每次都归零，保持上次的速度值
        speed = self.last_remote_speed

        if self.motor_enabled_via_rc:
            if not emergency_stop and (
                pwm_ch_value >= (1500 + self.DEAD_ZONE)
                or pwm_ch_value <= (1500 - self.DEAD_ZONE)
            ):
                # 简单的速度映射逻辑：1500映射为0，上半部分映射为正速度，下半部分映射为负速度
                if pwm_ch_value > 1500:
                    # 上半部分：1500到2000 -> 0到最大正速度
                    speed = (pwm_ch_value - 1500) * max_speed_limit / (2000 - 1500)
                else:
                    # 下半部分：1000到1500 -> 最大负速度到0
                    speed = (pwm_ch_value - 1500) * max_speed_limit / (1500 - 1000)
            elif pwm_ch_value == 1500:
                # 在死区内，速度设为0
                speed = 0.0
            elif emergency_stop:
                # 急停模式下只允许正速度（放缆），阻止负速度（收缆）
                if pwm_ch_value >= (1500 + self.DEAD_ZONE):
                    # 上半部分：1500到2000 -> 0到最大正速度（放缆）
                    speed = (pwm_ch_value - 1500) * max_speed_limit / (2000 - 1500)
                elif pwm_ch_value <= (1500 - self.DEAD_ZONE):
                    # 下半部分：在急停状态下强制阻止负速度（收缆）
                    speed = 0.0
                else:
                    # 死区内
                    speed = 0.0
        else:
            # 电机失能时，速度为0
            speed = 0.0

        # 更新last_remote_speed用于断连时保持状态
        self.last_remote_speed = speed

        # 更新PWM输出（如果PWM控制器可用且有有效通道数据）
        self._update_pwm_output(config, channels)

        # 在遥控器模式下，使能状态由遥控器决定，返回计算出的速度
        if self.motor_enabled_via_rc and current_mode == "remote":
            return speed
        else:
            return 0.0

    def is_motor_enabled_via_rc(self) -> bool:
        """获取遥控器使能状态"""
        return self.motor_enabled_via_rc

    def get_sbus_data(self) -> dict:
        """获取SBUS数据用于广播"""
        connection_status = self.mavlink_handler.get_connection_status()

        # 获取当前通道数据
        channels = self.mavlink_handler.get_channels()

        if self.mavlink_handler.is_connected() and channels:
            channels_data = channels
            signal_detected = True
            lost_frame = False
            failsafe = False
        else:
            # 连接断开或无通道数据时，提供模拟数据用于测试界面
            channels_data = [
                1500,
                1500,
                1000,
                1500,
                1000,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
                1500,
            ]
            signal_detected = False  # 标记为模拟数据
            lost_frame = True
            failsafe = True

        return {
            "signal_detected": signal_detected,
            "channels": channels_data,
            "failSafe": failsafe,
            "lostFrame": lost_frame,
            "connection_status": connection_status,  # 添加详细连接状态
        }

    def _update_pwm_output(self, config: dict, channels):
        """更新PWM输出"""
        if not self.pwm_controller or not self.pwm_controller.is_initialized():
            return

        if not channels:
            # 没有有效通道数据时，停止PWM输出
            self.pwm_controller.stop_pwm()
            return

        try:
            # 获取PWM通道配置
            pwm1_channel = config.get("pwm1_channel", 12)  # 默认通道12
            pwm2_channel = config.get("pwm2_channel", 13)  # 默认通道13

            # 转换为索引（通道编号从1开始，索引从0开始）
            pwm1_channel_idx = pwm1_channel - 1
            pwm2_channel_idx = pwm2_channel - 1

            # 更新PWM输出
            self.pwm_controller.update_pwm_from_channels_with_config(
                channels, pwm1_channel_idx, pwm2_channel_idx
            )

        except Exception as e:
            logger.error(f"更新PWM输出时出错: {e}")

    def set_pwm_controller(self, pwm_controller):
        """设置PWM控制器引用"""
        self.pwm_controller = pwm_controller


class PCControlHandler:
    """PC控制处理类"""

    def __init__(self, emergency_gpio: EmergencyStopGPIO):
        self.emergency_gpio = emergency_gpio
        self.latest_motor_command = {"type": "idle", "speed": 0.0}
        self.command_lock = threading.Lock()
        self.web_motor_enabled = False  # web界面设置的电机使能状态

    def handle_pc_control(
        self, config: dict, motor_data: dict, current_mode: str
    ) -> float:
        """处理PC控制逻辑，返回计算出的速度值"""
        final_speed = 0.0

        if self.web_motor_enabled and current_mode == "pc":  # 使用web界面设置的使能状态
            # 直接使用最新命令，无需队列
            with self.command_lock:
                if self.latest_motor_command["type"] == "set_speed":
                    final_speed = self.latest_motor_command["speed"]
                    # 根据圈数决定速度限制：前几圈使用initial_max_speed，之后使用max_speed
                    max_speed_limit = (
                        config.get("initial_max_speed", 10)
                        if motor_data["total_rounds"] <= config.get("limit_rounds", 100)
                        else config.get("max_speed", 10.0)
                    )
                    final_speed = max(
                        -max_speed_limit, min(max_speed_limit, final_speed)
                    )

                    # 急停状态下只允许正速度（放缆），阻止负速度（收缆）
                    if (
                        self.emergency_gpio.is_emergency_stop_active()
                        and final_speed < 0
                    ):
                        final_speed = 0.0  # 强制阻止负速度（收缆）
                        logger.warning(
                            "急停状态下阻止负速度（收缆），只允许正速度（放缆）"
                        )
                else:
                    final_speed = 0.0

        return final_speed

    def set_motor_command(self, command_type: str, speed: float = 0.0):
        """设置电机命令"""
        with self.command_lock:
            if self.web_motor_enabled:
                # 急停状态下只允许正速度（放缆），阻止负速 度（收缆）
                if self.emergency_gpio.is_emergency_stop_active() and speed < 0:
                    speed = 0.0  # 强制阻止负速度（收缆）
                    logger.warning(
                        "急停状态下阻止负速度设置（收缆），只允许正速度（放缆）"
                    )
                self.latest_motor_command = {"type": command_type, "speed": speed}
            else:
                self.latest_motor_command = {"type": command_type, "speed": 0.0}

    def enable_motor(self):
        """使能电机"""
        self.web_motor_enabled = True

    def disable_motor(self):
        """失能电机"""
        self.web_motor_enabled = False

    def is_motor_enabled(self) -> bool:
        """获取电机使能状态"""
        return self.web_motor_enabled
