import logging
import time
import subprocess
from typing import Optional, List
from pymavlink import mavutil

logger = logging.getLogger(__name__)


class MavlinkHandler:
    """MAVLink连接和数据处理类"""

    def __init__(self):
        # 连接优先级列表：UDP优先，然后依次尝试串口设备
        self.connection_priorities = [
            "udp:127.0.0.1:14555",
            "/dev/ttyACM0",
            "/dev/ttyUSB0",
        ]
        self.current_connection_string = None
        self.current_connection_index = 0
        self.master: Optional[mavutil.mavlink_connection] = None
        self.mavlink_connected = False
        self.last_mavlink_update_time = 0  # 上次收到有效消息的时间
        self.connection_established_time = 0  # 连接建立时间
        self.last_connection_attempt = 0
        self.CONNECTION_RETRY_INTERVAL = 3000  # 3秒重试间隔，减少等待时间
        self.CONNECTION_TIMEOUT = 2000  # 2秒无有效消息认为连接丢失
        self.CONNECTION_ESTABLISH_TIMEOUT = 5000  # 5秒内必须收到有效消息
        self.connection_lost = False

        # 非阻塞连接验证相关
        self.pending_connection = None  # 待验证的连接
        self.pending_connection_start_time = 0  # 开始验证时间
        self.pending_connection_string = None  # 待验证的连接字符串

        # 失败连接的黑名单（避免重复尝试验证失败的连接）
        self.failed_connections = {}  # {connection_string: fail_time}
        self.FAILED_CONNECTION_COOLDOWN = 10000  # 10秒内不重试验证失败的连接

    def _cleanup_connection(self, connection):
        """安全地清理连接"""
        if connection:
            try:
                connection.close()
                time.sleep(0.1)  # 给端口时间完全释放
            except:
                pass

    def _is_port_available(self, connection_string: str) -> bool:
        """检查端口是否可用（简单检查）"""
        if connection_string.startswith("/dev/"):
            try:
                # 检查设备文件是否存在
                import os

                return os.path.exists(connection_string)
            except:
                return False
        return True  # UDP端口总是可用

    def _cleanup_failed_connections(self, current_time: int):
        """清理过期的失败连接记录"""
        expired_connections = []
        for connection_string, fail_time in self.failed_connections.items():
            if current_time - fail_time >= self.FAILED_CONNECTION_COOLDOWN:
                expired_connections.append(connection_string)

        for connection_string in expired_connections:
            del self.failed_connections[connection_string]
            logger.debug(f"移除过期的失败连接记录: {connection_string}")

    def _check_mavlink_routerd(self) -> bool:
        """检查mavlink-routerd是否在运行"""
        try:
            result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
            return "mavlink-routerd" in result.stdout
        except:
            return False

    def _try_single_connection(self, connection_string: str) -> bool:
        """尝试单个连接（非阻塞方式）"""
        try:
            # 检查是否在失败连接黑名单中
            current_time = int(time.time() * 1000)
            if connection_string in self.failed_connections:
                fail_time = self.failed_connections[connection_string]
                if current_time - fail_time < self.FAILED_CONNECTION_COOLDOWN:
                    logger.debug(f"连接 {connection_string} 在冷却期内，跳过")
                    return False
                else:
                    # 冷却期结束，移除黑名单
                    del self.failed_connections[connection_string]

            logger.info(f"尝试连接: {connection_string}")

            # 检查端口是否可用
            if not self._is_port_available(connection_string):
                logger.debug(f"端口 {connection_string} 不可用")
                return False

            # 对于串口连接，检查mavlink-routerd是否占用
            if connection_string.startswith("/dev/") and self._check_mavlink_routerd():
                logger.warning(
                    f"检测到mavlink-routerd正在运行，可能占用串口设备 {connection_string}"
                )
                return False

            # 确保之前的连接已经完全关闭
            if self.master:
                self._cleanup_connection(self.master)
                self.master = None

            # 创建连接
            if connection_string.startswith("/dev/"):
                master = mavutil.mavlink_connection(connection_string, baud=115200)
            else:
                master = mavutil.mavlink_connection(connection_string)

            # 非阻塞方式检查心跳
            try:
                master.wait_heartbeat(timeout=0.5)  # 减少到0.5秒
                logger.info(
                    f"MAVLink连接建立: {connection_string}，系统ID：{master.target_system}"
                )

                # 设置为待验证状态，不阻塞
                self.pending_connection = master
                self.pending_connection_string = connection_string
                self.pending_connection_start_time = int(time.time() * 1000)
                logger.info("开始非阻塞验证控制消息...")
                return True  # 连接建立成功，但还需要验证

            except Exception as e:
                # 心跳超时，连接失败
                logger.debug(f"连接 {connection_string} 心跳检测失败: {e}")
                self._cleanup_connection(master)
                return False

        except Exception as e:
            logger.debug(f"连接 {connection_string} 失败: {e}")
            return False

    def _handle_pending_connection(self, current_time: int) -> bool:
        """处理待验证的连接（非阻塞）"""
        if not self.pending_connection:
            return False

        # 检查验证超时（3秒，给更多时间）
        if current_time - self.pending_connection_start_time > 3000:
            logger.warning(
                f"连接 {self.pending_connection_string} 验证超时，没有收到有效控制消息"
            )
            self._cleanup_connection(self.pending_connection)

            # 将验证失败的连接加入黑名单
            self.failed_connections[self.pending_connection_string] = current_time
            logger.debug(f"将 {self.pending_connection_string} 加入失败连接黑名单")

            # 设置连接丢失状态，触发下一个优先级的尝试
            self.connection_lost = True
            self.current_connection_string = self.pending_connection_string
            self.pending_connection = None
            self.pending_connection_string = None
            return False

        # 非阻塞检查消息
        try:
            msg = self.pending_connection.recv_match(blocking=False)
            if msg:
                # 扩展有效消息类型，包括心跳消息
                valid_msg_types = [
                    "RC_CHANNELS",
                    "RC_CHANNELS_RAW",
                    "SERVO_OUTPUT_RAW",
                    "HEARTBEAT",
                ]
                if msg.get_type() in valid_msg_types:
                    # 找到有效消息，验证成功
                    logger.info(f"收到有效消息: {msg.get_type()}，连接验证成功")
                    self.master = self.pending_connection
                    self.current_connection_string = self.pending_connection_string
                    self.mavlink_connected = True
                    self.connection_lost = False
                    self.last_mavlink_update_time = current_time

                    # 清理待验证状态
                    self.pending_connection = None
                    self.pending_connection_string = None
                    return True
        except Exception as e:
            # 检查是否是端口被占用的错误
            if "device reports readiness to read but returned no data" in str(e):
                logger.warning(
                    f"连接 {self.pending_connection_string} 可能被其他程序占用: {e}"
                )
            else:
                logger.warning(f"验证连接时出错: {e}")
            self._cleanup_connection(self.pending_connection)
            self.pending_connection = None
            self.pending_connection_string = None
            # 设置连接丢失，尝试下一个端口
            self.connection_lost = True
            return False

        # 还在验证中，返回False但不报错
        return False

    def connect(self) -> bool:
        """按优先级尝试连接MAVLink"""
        current_time = int(time.time() * 1000)

        # 如果有待验证的连接，不要尝试新连接
        if self.pending_connection:
            return False

        # 避免频繁重连，每3秒尝试一次
        if (
            current_time - self.last_connection_attempt
            <= self.CONNECTION_RETRY_INTERVAL
        ):
            return False

        self.last_connection_attempt = current_time

        # 清理过期的失败连接记录
        self._cleanup_failed_connections(current_time)

        # 确定起始连接索引
        start_index = 0
        if self.connection_lost and self.current_connection_string:
            # 如果当前有连接但已断开，从下一个优先级开始尝试
            try:
                start_index = (
                    self.connection_priorities.index(self.current_connection_string) + 1
                ) % len(self.connection_priorities)
            except ValueError:
                start_index = 0
        else:
            # 否则从最高优先级开始（UDP优先）
            start_index = 0

        # 尝试所有连接方式，从起始索引开始
        for i in range(len(self.connection_priorities)):
            connection_index = (start_index + i) % len(self.connection_priorities)
            connection_string = self.connection_priorities[connection_index]

            if self._try_single_connection(connection_string):
                self.current_connection_index = connection_index
                # 连接建立成功，但可能还在验证中
                return True
            else:
                logger.debug(f"连接 {connection_string} 失败，尝试下一个")

        # 所有连接都失败
        logger.warning("所有MAVLink连接方式都失败")
        self.mavlink_connected = False
        self.master = None
        self.current_connection_string = None
        return False

    def update_messages(self) -> bool:
        """更新MAVLink消息，返回是否有新消息"""
        current_time = int(time.time() * 1000)

        # 处理待验证的连接
        if self.pending_connection:
            return self._handle_pending_connection(current_time)

        if not self.master:
            return False

        msg_count = 0
        has_new_messages = False

        # 检查连接超时（只有在有有效连接时才检查）
        if self.mavlink_connected and self.last_mavlink_update_time > 0:
            time_diff = current_time - self.last_mavlink_update_time
            if time_diff > self.CONNECTION_TIMEOUT:
                logger.warning(f"MAVLink有效消息超时，时间差: {time_diff}ms")
                self.connection_lost = True
                self.mavlink_connected = False
                # 关闭当前连接，强制重新连接
                try:
                    self.master.close()
                except:
                    pass
                self.master = None
                self.current_connection_string = None
                return False

        # 实时接收所有可用的MAVLink消息，确保获取最新数据
        try:
            while True:
                any_msg = self.master.recv_match(blocking=False)
                if any_msg is None:
                    break  # 没有更多消息
                msg_count += 1
                has_new_messages = True

                # 扩展有效消息类型，包括心跳消息
                valid_msg_types = [
                    "RC_CHANNELS",
                    "RC_CHANNELS_RAW",
                    "SERVO_OUTPUT_RAW",
                    "HEARTBEAT",
                ]
                if any_msg.get_type() in valid_msg_types:
                    self.last_mavlink_update_time = current_time

                # 限制单次循环处理的消息数量，避免阻塞
                if msg_count >= 50:  # 最多处理50条消息
                    break
        except Exception as e:
            # 检查是否是端口被占用的错误
            if "device reports readiness to read but returned no data" in str(e):
                logger.warning(f"MAVLink连接可能被其他程序占用，将重新连接: {e}")
            else:
                logger.error(f"接收MAVLink消息时出错: {e}")

            # 关闭当前连接并标记为丢失
            self._cleanup_connection(self.master)
            self.master = None
            self.connection_lost = True
            self.mavlink_connected = False
            return False

        return has_new_messages

    def get_channels(self) -> Optional[List[int]]:
        """获取当前通道数据，连接断开时返回None"""
        if (
            not self.master
            or not self.master.messages.get("RC_CHANNELS")
            or self.connection_lost
        ):
            return None

        # 获取RC_CHANNELS消息快照，避免在函数执行过程中被更新
        rc_msg = self.master.messages["RC_CHANNELS"]
        channels = [
            rc_msg.chan1_raw,
            rc_msg.chan2_raw,
            rc_msg.chan3_raw,
            rc_msg.chan4_raw,
            rc_msg.chan5_raw,
            rc_msg.chan6_raw,
            rc_msg.chan7_raw,
            rc_msg.chan8_raw,
            rc_msg.chan9_raw,
            rc_msg.chan10_raw,
            rc_msg.chan11_raw,
            rc_msg.chan12_raw,
            rc_msg.chan13_raw,
            rc_msg.chan14_raw,
            rc_msg.chan15_raw,
            rc_msg.chan16_raw,
        ]

        return channels

    def is_connected(self) -> bool:
        """检查是否连接"""
        return (
            self.mavlink_connected
            and self.master is not None
            and not self.connection_lost
        )

    def has_valid_connection(self) -> bool:
        """检查是否有有效的MAVLink连接和通道数据"""
        return self.is_connected() and self.master.messages.get("RC_CHANNELS")

    def is_connection_lost(self) -> bool:
        """检查连接是否丢失"""
        return self.connection_lost

    def get_connection_status(self) -> dict:
        """获取连接状态信息，确保所有数据都可以JSON序列化"""
        current_time = int(time.time() * 1000)
        time_since_last_update = (
            current_time - self.last_mavlink_update_time
            if self.last_mavlink_update_time > 0
            else -1
        )

        return {
            "connected": bool(self.mavlink_connected),
            "connection_lost": bool(self.connection_lost),
            "has_channels": bool(self.has_valid_connection()),
            "last_update": int(self.last_mavlink_update_time),
            "time_since_last_update": int(time_since_last_update),
            "current_connection": (
                str(self.current_connection_string)
                if self.current_connection_string
                else None
            ),
            "connection_timeout": bool(
                time_since_last_update > self.CONNECTION_TIMEOUT
                if time_since_last_update > 0
                else False
            ),
        }
