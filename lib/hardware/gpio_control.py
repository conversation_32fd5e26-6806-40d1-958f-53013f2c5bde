import logging
import threading
import time
import wiringpi

logger = logging.getLogger(__name__)


class EmergencyStopGPIO:
    """急停GPIO控制类"""

    def __init__(self, gpio_pin=27):
        """
        初始化急停GPIO

        Args:
            gpio_pin: GPIO引脚编号（wiringPi编号）
        """
        self.gpio_pin = gpio_pin
        self.gpio_initialized = False
        self.emergency_stop = False
        self.running_flag = True
        self.monitor_thread = None

    def setup(self):
        """初始化急停GPIO设置"""
        try:
            # 初始化wiringPi库
            if wiringpi.wiringPiSetup() == -1:
                logger.error("错误：无法初始化wiringPi库")
                return False

            # 设置引脚为输入模式
            wiringpi.pinMode(self.gpio_pin, wiringpi.INPUT)

            # 根据用户偏好使用下拉电阻（正常状态为低电平，急停时为高电平）
            wiringpi.pullUpDnControl(self.gpio_pin, wiringpi.PUD_DOWN)

            self.gpio_initialized = True
            logger.info(f"急停GPIO引脚 {self.gpio_pin} 已设置为输入模式（带下拉电阻）")
            return True
        except Exception as e:
            logger.error(f"初始化急停GPIO时出错: {e}")
            return False

    def read_gpio(self):
        """读取急停GPIO引脚的电平状态"""
        if not self.gpio_initialized:
            return False
        try:
            level = wiringpi.digitalRead(self.gpio_pin)
            return level == 1  # 高电平表示急停激活
        except Exception as e:
            logger.error(f"读取急停GPIO时出错: {e}")
            return False

    def start_monitor(self):
        """启动急停监控任务"""
        if not self.gpio_initialized:
            logger.warning("GPIO未初始化，无法启动监控任务")
            return False

        self.running_flag = True
        self.monitor_thread = threading.Thread(target=self._monitor_task, daemon=True)
        self.monitor_thread.start()
        logger.info("急停监控任务已启动")
        return True

    def stop_monitor(self):
        """停止急停监控任务"""
        self.running_flag = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            logger.info("等待急停监控任务完成...")
            self.monitor_thread.join(timeout=3)  # 最多等待3秒
            if self.monitor_thread.is_alive():
                logger.warning("急停监控任务未能在指定时间内退出！")

    def _monitor_task(self):
        """急停监控任务"""
        logger.info("急停监控任务开始运行")

        last_emergency_state = False
        debounce_count = 0
        DEBOUNCE_THRESHOLD = 3  # 需要连续3次读取相同状态才确认状态变化

        while self.running_flag:
            try:
                current_gpio_state = self.read_gpio()

                # 防抖处理
                if current_gpio_state == last_emergency_state:
                    debounce_count += 1
                    if debounce_count >= DEBOUNCE_THRESHOLD:
                        # 状态稳定，更新急停状态
                        if self.emergency_stop != current_gpio_state:
                            self.emergency_stop = current_gpio_state
                            logger.info(
                                f"🚨 急停状态变化: {'激活' if self.emergency_stop else '解除'} (GPIO{self.gpio_pin}: {'高电平' if current_gpio_state else '低电平'})"
                            )
                else:
                    # 状态发生变化，重置防抖计数器
                    debounce_count = 0
                    last_emergency_state = current_gpio_state

            except Exception as e:
                logger.error(f"急停监控任务错误: {e}")

            time.sleep(0.05)  # 20Hz检测频率，足够快速响应急停

        logger.info("急停监控任务已停止")

    def is_emergency_stop_active(self):
        """获取当前急停状态"""
        return self.emergency_stop

    def is_initialized(self):
        """检查GPIO是否已初始化"""
        return self.gpio_initialized


class PWMController:
    """PWM控制类"""

    def __init__(self, pwm1_pin=21, pwm2_pin=22, duty_limit=0.5):
        """
        初始化PWM控制器

        Args:
            pwm1_pin: PWM1引脚编号（wiringPi编号）
            pwm2_pin: PWM2引脚编号（wiringPi编号）
            duty_limit: 占空比限制（0.0-1.0）
        """
        self.pwm1_pin = pwm1_pin
        self.pwm2_pin = pwm2_pin
        self.duty_limit = duty_limit
        self.pwm_initialized = False
        self.pwm_range = 100  # PWM范围，wiringPi软件PWM频率 = 1000000/(range*100) = 1000000/(100*100) = 100Hz

        # PWM当前值
        self.pwm1_value = 0
        self.pwm2_value = 0

    def setup(self):
        """初始化PWM设置"""
        try:
            # wiringPi库应该已经在EmergencyStopGPIO中初始化了
            # 如果没有初始化，这里再初始化一次
            if wiringpi.wiringPiSetup() == -1:
                logger.error("错误：无法初始化wiringPi库")
                return False

            # 设置引脚为输出模式
            wiringpi.pinMode(self.pwm1_pin, wiringpi.OUTPUT)
            wiringpi.pinMode(self.pwm2_pin, wiringpi.OUTPUT)

            # 创建软件PWM，范围0-10，频率 = 1000000/(10*100) = 1kHz
            if wiringpi.softPwmCreate(self.pwm1_pin, 0, self.pwm_range) != 0:
                logger.error(f"创建PWM1失败，引脚{self.pwm1_pin}")
                return False

            if wiringpi.softPwmCreate(self.pwm2_pin, 0, self.pwm_range) != 0:
                logger.error(f"创建PWM2失败，引脚{self.pwm2_pin}")
                return False

            self.pwm_initialized = True
            logger.info(
                f"PWM控制器初始化成功: PWM1引脚{self.pwm1_pin}, PWM2引脚{self.pwm2_pin}, 占空比限制{self.duty_limit}"
            )
            return True

        except Exception as e:
            logger.error(f"初始化PWM控制器时出错: {e}")
            return False

    def update_pwm_from_channels(self, channels):
        """
        根据遥控器通道数据更新PWM输出

        Args:
            channels: 遥控器通道数据列表，已归一化到(1000, 2000)范围
            pwm1_channel: PWM1对应的通道索引
            pwm2_channel: PWM2对应的通道索引
        """
        if not self.pwm_initialized:
            return False

        try:
            # 从配置中获取通道索引（需要传入配置）
            # 这里暂时使用默认值，后面会从配置中读取
            return True

        except Exception as e:
            logger.error(f"更新PWM输出时出错: {e}")
            return False

    def update_pwm_from_channels_with_config(
        self, channels, pwm1_channel_idx, pwm2_channel_idx
    ):
        """
        根据遥控器通道数据和配置更新PWM输出

        Args:
            channels: 遥控器通道数据列表，已归一化到(1000, 2000)范围
            pwm1_channel_idx: PWM1对应的通道索引（从0开始）
            pwm2_channel_idx: PWM2对应的通道索引（从0开始）
        """
        if not self.pwm_initialized:
            return False

        try:
            # 获取通道值，确保在有效范围内
            if pwm1_channel_idx < len(channels):
                pwm1_ch = max(1000, min(2000, channels[pwm1_channel_idx]))
            else:
                pwm1_ch = 1500  # 默认中位值

            if pwm2_channel_idx < len(channels):
                pwm2_ch = max(1000, min(2000, channels[pwm2_channel_idx]))
            else:
                pwm2_ch = 1500  # 默认中位值

            # 应用死区处理（类似原程序逻辑）
            if pwm1_ch < 1100 :
                pwm1_ch = 1000
            if pwm2_ch < 1100 :
                pwm2_ch = 1000

            # 将通道值映射到PWM输出范围（0-10）
            pwm1_raw = int((pwm1_ch - 1000) * self.pwm_range / (2000 - 1000))
            pwm2_raw = int((pwm2_ch - 1000) * self.pwm_range / (2000 - 1000))

            # 应用占空比限制
            self.pwm1_value = int(pwm1_raw * self.duty_limit)
            self.pwm2_value = int(pwm2_raw * self.duty_limit)

            # 确保值在有效范围内
            self.pwm1_value = max(0, min(self.pwm_range, self.pwm1_value))
            self.pwm2_value = max(0, min(self.pwm_range, self.pwm2_value))

            # 输出PWM信号
            wiringpi.softPwmWrite(self.pwm1_pin, self.pwm1_value)
            wiringpi.softPwmWrite(self.pwm2_pin, self.pwm2_value)

            return True

        except Exception as e:
            logger.error(f"更新PWM输出时出错: {e}")
            return False

    def set_pwm_values(self, pwm1_value, pwm2_value):
        """
        直接设置PWM值

        Args:
            pwm1_value: PWM1值（0-100）
            pwm2_value: PWM2值（0-100）
        """
        if not self.pwm_initialized:
            return False

        try:
            # 应用占空比限制
            self.pwm1_value = int(
                max(0, min(self.pwm_range, pwm1_value)) * self.duty_limit
            )
            self.pwm2_value = int(
                max(0, min(self.pwm_range, pwm2_value)) * self.duty_limit
            )

            # 输出PWM信号
            wiringpi.softPwmWrite(self.pwm1_pin, self.pwm1_value)
            wiringpi.softPwmWrite(self.pwm2_pin, self.pwm2_value)

            return True

        except Exception as e:
            logger.error(f"设置PWM值时出错: {e}")
            return False

    def stop_pwm(self):
        """停止PWM输出"""
        if not self.pwm_initialized:
            return False

        try:
            wiringpi.softPwmWrite(self.pwm1_pin, 0)
            wiringpi.softPwmWrite(self.pwm2_pin, 0)
            self.pwm1_value = 0
            self.pwm2_value = 0
            return True

        except Exception as e:
            logger.error(f"停止PWM输出时出错: {e}")
            return False

    def get_pwm_values(self):
        """获取当前PWM值"""
        return {
            "pwm1_value": self.pwm1_value,
            "pwm2_value": self.pwm2_value,
            "pwm1_pin": self.pwm1_pin,
            "pwm2_pin": self.pwm2_pin,
            "duty_limit": self.duty_limit,
        }

    def is_initialized(self):
        """检查PWM是否已初始化"""
        return self.pwm_initialized
