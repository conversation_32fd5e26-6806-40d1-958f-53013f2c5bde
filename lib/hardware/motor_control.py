import socket
import struct
import time
import select
from enum import Enum
from dataclasses import dataclass
from typing import Optional
import logging

print("Loading MotorCANControl.py version with is_enabled attribute")

# 电机参数定义
RAD2ROUND = 1.0 / (2 * 3.141592653589793)
DM_J8006_2EC_P_MAX = 12.5  # 位置最大值 rad
DM_J8006_2EC_V_MAX = 45.0  # 速度最大值 rad/s
DM_J8006_2EC_T_MAX = 20.0  # 扭矩最大值 Nm
DM_J8006_2EC_V_LIMIT = 5.0  # 速度限制 rad/s
DM_J8006_2EC_KP_MIN = 0.0  # MIT模式KP最小值
DM_J8006_2EC_KP_MAX = 500.0  # MIT模式KP最大值
DM_J8006_2EC_KD_MIN = 0.0  # MIT模式KD最小值
DM_J8006_2EC_KD_MAX = 5.0  # MIT模式KD最大值

# 寄存器操作的CAN ID
REGISTER_CMD_ID = 0x7FF


class Mode(Enum):
    MIT = 0x01  # MIT模式
    POS_VEL = 0x02  # 位置速度模式
    VEL = 0x03  # 速度模式


class ErrorState(Enum):
    MOTOR_DISABLED = 0x00  # 失能状态
    MOTOR_ENABLED = 0x01  # 使能状态
    OVER_VOLTAGE = 0x08  # 超压
    UNDER_VOLTAGE = 0x09  # 欠压
    OVER_CURRENT = 0x0A  # 过电流
    MOS_TEMP = 0x0B  # MOS过温
    COIL_TEMP = 0x0C  # 电机线圈过温
    COMM_LOST = 0x0D  # 通讯丢失
    OVERLOAD = 0x0E  # 过载

    @classmethod
    def get_description(cls, error_state):
        descriptions = {
            cls.MOTOR_DISABLED: "电机失能",
            cls.MOTOR_ENABLED: "电机使能",
            cls.OVER_VOLTAGE: "超压保护",
            cls.UNDER_VOLTAGE: "欠压保护",
            cls.OVER_CURRENT: "过流保护",
            cls.MOS_TEMP: "MOS过温保护",
            cls.COIL_TEMP: "电机过温保护",
            cls.COMM_LOST: "通信丢失",
            cls.OVERLOAD: "过载保护",
        }
        return descriptions.get(error_state, "未知错误")


class Register(Enum):
    MODE = 0x0A  # 电机模式寄存器
    ENABLE_STATE = 0x0B  # 使能状态寄存器


class Command(Enum):
    READ = 0x33  # 读取寄存器指令
    WRITE = 0x55  # 写入寄存器指令
    STORE = 0xAA  # 保存寄存器指令


@dataclass
class MotorStatus:
    id: int = 0
    error: int = 0
    position: float = 0.0
    velocity: float = 0.0
    torque: float = 0.0
    temp_mos: int = 0
    temp_rotor: int = 0
    current_round: float = 0.0
    last_round: float = 0.0
    delta_rounds: float = 0.0
    round_cnt: int = 0
    total_rounds: float = 0.0
    offset_round: float = 0.0
    msg_cnt: int = 0
    error_state: ErrorState = ErrorState.MOTOR_DISABLED
    error_description: str = ""
    is_enabled: bool = False
    angle: float = 0.0

    def update_error_description(self):
        self.error_description = ErrorState.get_description(self.error_state)


class MotorCANControl:
    # CAN通信ID定义
    MOTOR_MASTER_ID = 0x00  # 主机ID(接收)
    MOTOR_MIT_ID = 0x00  # MIT模式ID
    MOTOR_POS_VEL_ID = 0x100  # 位置速度模式ID
    MOTOR_VEL_ID = 0x200  # 速度模式ID

    def __init__(self, can_interface: str = "can0", motor_id: int = 0x01):
        self.can_interface = can_interface
        self.motor_id = motor_id
        self.current_mode = Mode.VEL
        self.initialized = False
        self.status = MotorStatus()
        self.socket = None
        self.timeout = 0.5  # 增加到500ms超时
        self.update_interval = 0.05  # 50ms更新间隔，20Hz频率
        self.last_update_time = 0
        self.last_communication_time = 0  # 上次成功通信时间
        self.communication_timeout = 2.0  # 通信超时时间（秒）

    def begin(self) -> bool:
        try:
            # 创建CAN套接字
            self.socket = socket.socket(socket.AF_CAN, socket.SOCK_RAW, socket.CAN_RAW)
            self.socket.bind((self.can_interface,))
            # 设置非阻塞模式
            self.socket.setblocking(False)
            return True
        except Exception as e:
            print(f"CAN初始化失败: {e}")
            return False

    def _wait_for_response(self, timeout: float = None) -> Optional[bytes]:
        if timeout is None:
            timeout = self.timeout

        start_time = time.time()
        while time.time() - start_time < timeout:
            readable, _, _ = select.select(
                [self.socket], [], [], 0.001
            )  # Short poll timeout
            if self.socket in readable:
                try:
                    frame = self.socket.recv(16)
                    can_id, dlc, data = struct.unpack("=IB3x8s", frame)

                    # Debug print all received CAN frames
                    logging.debug(
                        f"[CAN_RECV] ID: 0x{can_id:03X}, DLC: {dlc}, Data: {' '.join([f'{b:02X}' for b in data])}"
                    )

                    # 返回所有收到的CAN帧，或者根据需要进行更复杂的过滤
                    # 这里暂时不进行过滤，让上层函数判断是否是期望的响应
                    return frame

                except BlockingIOError:
                    continue
                except Exception as e:
                    logging.error(
                        f"[CAN_ERROR] _wait_for_response frame parsing failed: {e}"
                    )
                    continue
            time.sleep(0.001)  # Short sleep to reduce CPU usage
        return None

    def set_mode(self, mode: Mode) -> bool:
        # 先失能电机
        self.disable()
        time.sleep(0.01)

        # 写入模式
        if self.write_register(Register.MODE, mode.value):
            time.sleep(0.01)
            self.enable()
            return True
        return False

    def get_current_mode(self) -> Optional[Mode]:
        value = self.read_register(Register.MODE)
        if value is not None:
            self.current_mode = Mode(value)
            return self.current_mode
        return None

    def save_config(self) -> bool:
        self.disable()
        time.sleep(0.01)
        success = self.store_register(Register.MODE)
        time.sleep(0.01)
        self.enable()
        return success

    def enable(self) -> bool:
        frame = self._create_can_frame(self._get_command_id(), [0xFF] * 7 + [0xFC])
        return self._send_can_frame(frame)

    def disable(self) -> bool:
        frame = self._create_can_frame(self._get_command_id(), [0xFF] * 7 + [0xFD])
        return self._send_can_frame(frame)

    def clear_error(self) -> bool:
        frame = self._create_can_frame(self._get_command_id(), [0xFF] * 7 + [0xFB])
        return self._send_can_frame(frame)

    def save_zero_position(self) -> bool:
        frame = self._create_can_frame(self._get_command_id(), [0xFF] * 7 + [0xFE])
        return self._send_can_frame(frame)

    def set_mit_mode(
        self, kp: float, kd: float, position: float, velocity: float, torque: float
    ) -> bool:
        frame = self._create_can_frame(
            self.MOTOR_MIT_ID + self.motor_id,
            self._pack_mit_command(kp, kd, position, velocity, torque),
        )
        return self._send_can_frame(frame)

    def set_position_velocity_mode(self, position: float, velocity: float) -> bool:
        frame = self._create_can_frame(
            self.MOTOR_POS_VEL_ID + self.motor_id,
            self._pack_position_velocity_command(position, velocity),
        )
        return self._send_can_frame(frame)

    def set_velocity_mode(self, velocity: float) -> bool:
        # 限制速度在-DM_J8006_2EC_V_MAX到DM_J8006_2EC_V_MAX之间
        velocity = max(-DM_J8006_2EC_V_MAX, min(DM_J8006_2EC_V_MAX, velocity))
        frame = self._create_can_frame(
            self.MOTOR_VEL_ID + self.motor_id, self._pack_velocity_command(velocity)
        )
        return self._send_can_frame(frame)

    def update_status(self) -> bool:
        try:
            # 检查更新间隔
            current_time = time.time()
            if current_time - self.last_update_time < self.update_interval:
                return False
            self.last_update_time = current_time

            # 使用非阻塞方式接收数据
            readable, _, _ = select.select([self.socket], [], [], 0)
            if not readable:
                return False

            frame = self.socket.recv(16)
            if not frame:
                return False

            can_id, dlc, data = struct.unpack("=IB3x8s", frame)

            # logging.debug(f"[UPDATE_STATUS] 收到CAN帧 - ID: 0x{can_id:03X}, DLC: {dlc}, Data: {' '.join([f'{b:02X}' for b in data])}")

            # 假设电机状态的CAN ID是MOTOR_MASTER_ID (0x00)，或者根据实际情况调整
            # 如果实际电机的状态CAN ID不是MOTOR_MASTER_ID，需要在这里修改
            if can_id == self.MOTOR_MASTER_ID:
                self._unpack_status(data)
                self._update_round_count()
                # 更新通信时间
                self.last_communication_time = time.time()
                # logging.debug(f"[UPDATE_STATUS] 电机状态已更新: is_enabled={self.status.is_enabled}, speed={self.status.velocity}")
                return True
            else:
                # logging.debug(f"[UPDATE_STATUS] 忽略非电机状态帧: ID 0x{can_id:03X}")
                return False  # 忽略非预期的CAN ID，等待正确的状态帧
        except BlockingIOError:
            logging.debug("[UPDATE_STATUS] 非阻塞IO错误，没有可读数据")
            return False
        except Exception as e:
            logging.error(f"[UPDATE_STATUS] 更新状态失败: {e}")
            return False

    def get_status(self) -> MotorStatus:
        return self.status

    def get_total_rounds(self) -> float:
        return self.status.total_rounds

    def get_current_speed(self) -> float:
        return self.status.velocity

    def is_communication_lost(self) -> bool:
        """检查CAN通信是否丢失"""
        if self.last_communication_time == 0:
            return True  # 从未收到过消息
        current_time = time.time()
        return (current_time - self.last_communication_time) > self.communication_timeout

    def shutdown(self):
        """关闭CAN套接字"""
        if self.socket:
            try:
                self.socket.close()
                self.socket = None  # 设置为None，避免重复关闭
                print("CAN套接字已关闭")
            except Exception as e:
                print(f"关闭CAN套接字时出错: {e}")
        else:
            print("CAN套接字已经关闭")

    def _get_command_id(self) -> int:
        """获取命令ID"""
        return self.MOTOR_MASTER_ID + self.motor_id

    def _create_can_frame(self, can_id: int, data: list) -> bytes:
        """创建CAN帧"""
        frame = struct.pack("=IB3x8s", can_id, len(data), bytes(data))
        return frame

    def _send_can_frame(self, frame: bytes) -> bool:
        """发送CAN帧"""
        if not self.socket:
            print("发送CAN帧失败: CAN套接字未初始化或已关闭")
            return False

        try:
            self.socket.send(frame)
            return True
        except Exception as e:
            print(f"发送CAN帧失败: {e}")
            return False

    def _pack_mit_command(
        self, kp: float, kd: float, position: float, velocity: float, torque: float
    ) -> list:
        pos_tmp = self._float_to_uint(
            position, -DM_J8006_2EC_P_MAX, DM_J8006_2EC_P_MAX, 16
        )
        vel_tmp = self._float_to_uint(
            velocity, -DM_J8006_2EC_V_MAX, DM_J8006_2EC_V_MAX, 12
        )
        tor_tmp = self._float_to_uint(
            torque, -DM_J8006_2EC_T_MAX, DM_J8006_2EC_T_MAX, 12
        )
        kp_tmp = self._float_to_uint(kp, DM_J8006_2EC_KP_MIN, DM_J8006_2EC_KP_MAX, 12)
        kd_tmp = self._float_to_uint(kd, DM_J8006_2EC_KD_MIN, DM_J8006_2EC_KD_MAX, 12)

        return [
            (pos_tmp >> 8) & 0xFF,
            pos_tmp & 0xFF,
            (vel_tmp >> 4) & 0xFF,
            ((vel_tmp & 0xF) << 4) | ((kp_tmp >> 8) & 0xF),
            kp_tmp & 0xFF,
            (kd_tmp >> 4) & 0xFF,
            ((kd_tmp & 0xF) << 4) | ((tor_tmp >> 8) & 0xF),
            tor_tmp & 0xFF,
        ]

    def _pack_position_velocity_command(self, position: float, velocity: float) -> list:
        pos_bytes = struct.pack("f", position)
        vel_bytes = struct.pack("f", velocity)
        return list(pos_bytes) + list(vel_bytes)

    def _pack_velocity_command(self, velocity: float) -> list:
        # 假设速度已经在外部被限制在期望的范围内，这里只进行打包
        vel_bytes = struct.pack("f", velocity)
        return list(vel_bytes)

    def _unpack_status(self, data: bytes):
        """解析状态数据"""
        # 解析ID和错误状态
        self.status.id = data[0] & 0x0F
        self.status.error = data[0] >> 4
        self.status.error_state = ErrorState(self.status.error)
        self.status.error_description = ErrorState.get_description(
            self.status.error_state
        )

        # 根据错误状态设置 is_enabled
        self.status.is_enabled = self.status.error_state == ErrorState.MOTOR_ENABLED

        # 解析位置
        pos_tmp = (data[1] << 8) | data[2]
        self.status.position = self._uint_to_float(
            pos_tmp, -DM_J8006_2EC_P_MAX, DM_J8006_2EC_P_MAX, 16
        )

        # 解析速度
        vel_tmp = (data[3] << 4) | ((data[4] & 0xF0) >> 4)
        self.status.velocity = self._uint_to_float(
            vel_tmp, -DM_J8006_2EC_V_MAX, DM_J8006_2EC_V_MAX, 12
        )

        # 解析扭矩
        tor_tmp = ((data[4] & 0x0F) << 8) | data[5]
        self.status.torque = self._uint_to_float(
            tor_tmp, -DM_J8006_2EC_T_MAX, DM_J8006_2EC_T_MAX, 12
        )

        # 解析温度
        self.status.temp_mos = data[6]
        self.status.temp_rotor = data[7]

        self.status.msg_cnt += 1

    def _update_round_count(self):
        """更新圈数计数"""
        if not self.initialized and self.status.msg_cnt > 50:
            self.status.current_round = (
                self.status.position + DM_J8006_2EC_P_MAX
            ) * RAD2ROUND
            self.status.offset_round = self.status.current_round
            self.initialized = True
            return

        if not self.initialized:
            return

        self.status.last_round = self.status.current_round
        self.status.current_round = (
            self.status.position + DM_J8006_2EC_P_MAX
        ) * RAD2ROUND

        if (
            self.status.current_round - self.status.last_round
            > DM_J8006_2EC_P_MAX * RAD2ROUND
        ):
            self.status.round_cnt -= 1
            self.status.delta_rounds = (
                self.status.current_round
                - self.status.last_round
                - (2.0 * DM_J8006_2EC_P_MAX * RAD2ROUND)
            )
        elif (
            self.status.current_round - self.status.last_round
            < -DM_J8006_2EC_P_MAX * RAD2ROUND
        ):
            self.status.round_cnt += 1
            self.status.delta_rounds = (
                self.status.current_round
                - self.status.last_round
                + (2.0 * DM_J8006_2EC_P_MAX * RAD2ROUND)
            )
        else:
            self.status.delta_rounds = (
                self.status.current_round - self.status.last_round
            )

        self.status.total_rounds = (
            float(self.status.round_cnt) * (2.0 * DM_J8006_2EC_P_MAX * RAD2ROUND)
            + self.status.current_round
            - self.status.offset_round
        )

    def _float_to_uint(self, x: float, x_min: float, x_max: float, bits: int) -> int:
        """将浮点数转换为无符号整数"""
        span = x_max - x_min
        offset = x_min
        return int(((x - offset) * ((1 << bits) - 1)) / span)

    def _uint_to_float(self, x: int, x_min: float, x_max: float, bits: int) -> float:
        """将无符号整数转换为浮点数"""
        span = x_max - x_min
        offset = x_min
        return ((float(x) * span) / ((1 << bits) - 1)) + offset

    def read_register(self, reg: Register) -> Optional[int]:
        command_data = [
            self.motor_id,
            0x00,
            Command.READ.value,
            reg.value,
            0x00,
            0x00,
            0x00,
            0x00,
        ]
        frame = self._create_can_frame(REGISTER_CMD_ID, command_data)

        for retry in range(3):
            print(
                f"[DEBUG] 尝试发送读取寄存器命令 {reg.name}... (第 {retry + 1} 次)\n"
            )  # Added newline
            if not self._send_can_frame(frame):
                print(
                    f"[WARNING] 发送读取寄存器命令 {reg.name} 失败。\n"
                )  # Added newline
                time.sleep(0.1)
                continue

            response_timeout = self.timeout
            start_response_time = time.time()
            while time.time() - start_response_time < response_timeout:
                print(
                    f"[DEBUG] 等待 {reg.name} 响应... (剩余时间: {response_timeout - (time.time() - start_response_time):.2f}s)\n"
                )  # Added newline
                response_frame = self._wait_for_response()  # Expect 0x000 frame

                if response_frame:
                    can_id, dlc, data = struct.unpack("=IB3x8s", response_frame)

                    # Validate response: ID should be MOTOR_MASTER_ID (0x000),
                    # and data should match expected format: [motor_id, 0x00, Command.READ.value, reg.value, value]
                    # Log shows successful read response as: ID: 0x000, DLC: 8, 数据: 01 00 33 0A 03 00 00 00
                    # Where 0x01 is motor_id, 0x00, 0x33 is READ, 0x0A is MODE, 0x03 is value
                    if (
                        can_id == self.MOTOR_MASTER_ID
                        and dlc == 8  # Added DLC check here
                        and data[0] == self.motor_id
                        and data[2] == Command.READ.value
                        and data[3] == reg.value
                    ):

                        value = struct.unpack("<I", data[4:8])[
                            0
                        ]  # Assuming the value is a 4-byte integer
                        print(
                            f"[DEBUG] 读取寄存器 {reg.name} 成功，值为: {value}\n"
                        )  # Added newline
                        return value
                    else:
                        print(
                            f"[DEBUG] 收到非预期0x000帧 (可能为状态更新)，等待 {reg.name} 响应...\n"
                        )  # Added newline
                        continue
                time.sleep(
                    0.001
                )  # Short sleep if no frame received to prevent busy-waiting

            print(
                f"[WARNING] 读取寄存器 {reg.name} 未收到预期响应，重试命令发送... (第 {retry + 1} 次)\n"
            )  # Added newline
            time.sleep(0.1)

        print(
            f"[ERROR] 读取寄存器 {reg.name} 失败，达到最大重试次数。\n"
        )  # Added newline
        return None

    def write_register(self, reg: Register, value: int) -> bool:
        # Prepare value for CAN frame (assuming 4 bytes for integer value)
        value_bytes = struct.pack("<I", value)
        command_data = [self.motor_id, 0x00, Command.WRITE.value, reg.value] + list(
            value_bytes
        )
        frame = self._create_can_frame(REGISTER_CMD_ID, command_data)

        for retry in range(3):
            print(
                f"[DEBUG] 尝试发送写入寄存器命令 {reg.name}... (第 {retry + 1} 次)\n"
            )  # Added newline
            if not self._send_can_frame(frame):
                print(
                    f"[WARNING] 发送写入寄存器命令 {reg.name} 失败。\n"
                )  # Added newline
                time.sleep(0.1)
                continue

            response_timeout = self.timeout
            start_response_time = time.time()
            while time.time() - start_response_time < response_timeout:
                print(
                    f"[DEBUG] 等待 {reg.name} 写入响应... (剩余时间: {response_timeout - (time.time() - start_response_time):.2f}s)\n"
                )  # Added newline
                response_frame = self._wait_for_response()  # Expect 0x000 frame

                if response_frame:
                    can_id, dlc, data = struct.unpack("=IB3x8s", response_frame)

                    # Validate response: ID should be MOTOR_MASTER_ID (0x000),
                    # and data should match expected format: [motor_id, 0x00, Command.WRITE.value, reg.value, value]
                    # Log shows successful write response as: ID: 0x000, DLC: 8, 数据: 01 00 55 0A 03 00 00 00 (assuming motor_id 0x01, mode 0x03)
                    if (
                        can_id == self.MOTOR_MASTER_ID
                        and dlc == 8  # Added DLC check here
                        and data[0] == self.motor_id
                        and data[2] == Command.WRITE.value
                        and data[3] == reg.value
                        and data[4:8] == value_bytes
                    ):

                        print(
                            f"[DEBUG] 写入寄存器 {reg.name} 成功。\n"
                        )  # Added newline
                        return True
                    else:
                        print(
                            f"[DEBUG] 收到非预期0x000帧 (可能为状态更新)，等待 {reg.name} 写入响应...\n"
                        )  # Added newline
                        continue
                time.sleep(
                    0.001
                )  # Short sleep if no frame received to prevent busy-waiting

            print(
                f"[WARNING] 写入寄存器 {reg.name} 未收到预期响应，重试命令发送... (第 {retry + 1} 次)\n"
            )  # Added newline
            time.sleep(0.1)

        print(
            f"[ERROR] 写入寄存器 {reg.name} 失败，达到最大重试次数。\n"
        )  # Added newline
        return False

    def store_register(self, reg: Register) -> bool:
        command_data = [
            self.motor_id,
            0x00,
            Command.STORE.value,
            reg.value,
            0x00,
            0x00,
            0x00,
            0x00,
        ]
        frame = self._create_can_frame(REGISTER_CMD_ID, command_data)

        print(
            f"[DEBUG_INIT] Command.STORE.value: {Command.STORE.value}\n"
        )  # Added newline
        print(f"[DEBUG_INIT] Register.MODE.value: {reg.value}\n")  # Added newline

        for retry in range(3):
            print(f"[DEBUG] 尝试发送存储寄存器命令 {reg.name}... (第 {retry + 1} 次)\n")
            if not self._send_can_frame(frame):
                print(f"[WARNING] 发送存储寄存器命令 {reg.name} 失败。\n")
                time.sleep(0.1)
                continue

            response_timeout = self.timeout
            start_response_time = time.time()
            while time.time() - start_response_time < response_timeout:
                print(
                    f"[DEBUG] 等待 {reg.name} 存储响应... (剩余时间: {response_timeout - (time.time() - start_response_time):.2f}s)\n"
                )  # Added newline
                response_frame = self._wait_for_response()  # Expect 0x000 frame

                if response_frame:
                    can_id, dlc, data = struct.unpack("=IB3x8s", response_frame)

                    print(
                        f"[DEBUG_VERBOSE] store_register: Received can_id=0x{can_id:03X}, dlc={dlc}, data={data.hex().upper()}\n"
                    )  # Added newline
                    print(
                        f"[DEBUG_VERBOSE] store_register: Checking (can_id == self.MOTOR_MASTER_ID): {can_id == self.MOTOR_MASTER_ID} (received 0x{can_id:03X}, expected 0x{self.MOTOR_MASTER_ID:03X})\n"
                    )  # Added newline
                    print(
                        f"[DEBUG_VERBOSE] store_register: Checking (dlc == 4): {dlc == 4} (received {dlc}, expected 4)\n"
                    )  # Added newline
                    print(
                        f"[DEBUG_VERBOSE] store_register: Checking (data[0] == self.motor_id): {data[0]} == {self.motor_id} -> {data[0] == self.motor_id}\n"
                    )  # Added newline
                    print(
                        f"[DEBUG_VERBOSE] store_register: Checking (data[2] == Command.STORE.value): {data[2]} == {Command.STORE.value} -> {data[2] == Command.STORE.value}\n"
                    )  # Added newline
                    print(
                        f"[DEBUG_VERBOSE] store_register: Checking (data[3] == 0x01): {data[3]} == 0x01 -> {data[3] == 0x01}\n"
                    )  # Added newline

                    if (
                        can_id == self.MOTOR_MASTER_ID
                        and dlc == 4  # Added DLC check here
                        and data[0] == self.motor_id
                        and data[2] == Command.STORE.value
                        and data[3] == 0x01
                    ):  # Check for the success indicator byte (0x01)

                        print(f"[DEBUG] 存储寄存器 {reg.name} 成功。\n")
                        return True
                    else:
                        print(
                            f"[DEBUG] 收到非预期0x000帧 (可能为状态更新，或存储响应格式不符)，等待 {reg.name} 存储响应...\n"
                        )
                        continue
                time.sleep(
                    0.001
                )  # Short sleep if no frame received to prevent busy-waiting

            print(
                f"[WARNING] 存储寄存器 {reg.name} 未收到预期响应，重试命令发送... (第 {retry + 1} 次)\n"
            )
            time.sleep(0.1)

        print(f"[ERROR] 存储寄存器 {reg.name} 失败，达到最大重试次数。\n")
        return False
