#!/usr/bin/env python3
import sys
import logging
import threading
import time
import asyncio
import uvicorn
from pathlib import Path
from typing import Optional
from contextlib import asynccontextmanager

# 强制重新加载模块以避免缓存问题
modules_to_reload = [
    "lib.hardware.motor_control",
    "lib.hardware.mavlink_handler",
    "lib.hardware.gpio_control",
    "lib.control.control_handler",
    "lib.web.server",
]

for module_name in modules_to_reload:
    if module_name in sys.modules:
        del sys.modules[module_name]

from lib.hardware import MotorCANControl, ErrorState, EmergencyStopGPIO, PWMController
from lib.hardware.mavlink_handler import MavlinkHandler
from lib.utils import (
    load_config,
    save_config,
    validate_config,
    get_default_config,
    calculate_current_cable_length,
)
from lib.control import RemoteControlHandler, PCControlHandler
from lib.web import create_app

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 全局退出标志和任务引用
running_flag = True
motor_control_thread: Optional[threading.Thread] = None
realtime_broadcast_task_ref: Optional[asyncio.Task] = None

# 创建急停GPIO实例
emergency_gpio = EmergencyStopGPIO()

# 创建PWM控制器实例（稍后在配置加载后重新初始化）
pwm_controller = None

# 全局变量
motor_data = {
    "is_enabled": False,
    "speed": 0.0,
    "total_rounds": 0.0,
    "angle": 0.0,
    "torque": 0.0,
    "temperature": 0.0,
    "error_state": "MOTOR_DISABLED",
    "error_description": "",
    "control_mode": "remote",  # 添加当前控制模式
}
current_mode = "remote"  # remote 或 pc

# 全局mavlink处理实例 - 使用新的优先级连接管理
mavlink_handler = MavlinkHandler()

# 创建遥控器控制处理实例
remote_control_handler = RemoteControlHandler(mavlink_handler)

# 创建PC控制处理实例
pc_control_handler = PCControlHandler(emergency_gpio)

# 配置更新相关
latest_config_update = None
config_lock = threading.Lock()

# 电机控制实例
motor = MotorCANControl(motor_id=0x01)


def motor_control_task():
    """电机控制任务"""
    global running_flag, current_mode, latest_config_update

    # 初始化CAN接口
    if not motor.begin():
        logger.error("CAN接口初始化失败，电机控制任务无法启动。")
        running_flag = False  # 如果初始化失败，设置退出标志
        return

    last_emergency_state = False

    # 启动时加载配置到缓存
    config = load_config()

    while running_flag:  # 检查退出标志
        try:
            # 处理MAVLink连接和消息更新
            if not mavlink_handler.is_connected():
                mavlink_handler.connect()

            # 无论连接状态如何，都要调用update_messages进行超时检测和待验证连接处理
            if mavlink_handler.master or mavlink_handler.pending_connection:
                mavlink_handler.update_messages()

            # 获取急停状态
            emergency_stop = emergency_gpio.is_emergency_stop_active()

            # 处理急停状态变化
            if emergency_stop != last_emergency_state:
                logger.info(
                    "⚠️ 急停"
                    + (
                        "激活！仅允许放缆操作"
                        if emergency_stop
                        else "解除，恢复正常控制"
                    )
                )
                last_emergency_state = emergency_stop

            # 检查MAVLink连接状态，决定实际控制模式
            mavlink_connected = mavlink_handler.is_connected()
            effective_mode = current_mode

            # 如果当前是遥控器模式但MAVLink断开，临时切换到Web模式避免卡顿
            if current_mode == "remote" and not mavlink_connected:
                effective_mode = "pc"
                # 注意：这里不修改global current_mode，只是临时切换控制逻辑

            # 根据有效模式处理控制逻辑
            if effective_mode == "remote":
                final_speed = remote_control_handler.handle_remote_control(
                    config, emergency_stop, motor_data, current_mode
                )
            else:
                final_speed = pc_control_handler.handle_pc_control(
                    config, motor_data, current_mode
                )

            # 统一的电机速度控制
            motor.set_velocity_mode(final_speed)

            # 检查CAN通信状态
            communication_lost = motor.is_communication_lost()

            # 更新电机状态
            if motor.update_status():
                status = motor.get_status()
                # 根据有效模式决定使能状态的来源
                if effective_mode == "remote":
                    # 遥控器模式：使能状态由遥控器决定
                    is_enabled = remote_control_handler.is_motor_enabled_via_rc()
                else:
                    # web模式：使能状态由web界面决定
                    is_enabled = pc_control_handler.is_motor_enabled()

                # 计算当前放线长度
                distance = -calculate_current_cable_length(-status.total_rounds, config)

                motor_data.update(
                    {
                        "is_enabled": is_enabled,
                        "speed": status.velocity,
                        "total_rounds": status.total_rounds,
                        "angle": distance,  # 使用计算出的放线长度
                        "torque": status.torque,
                        "temperature": status.temp_rotor,
                        "error_state": status.error_state.name,
                        "error_description": status.error_description,
                        "control_mode": current_mode,
                        "emergency_stop": emergency_gpio.is_emergency_stop_active(),
                        "communication_lost": communication_lost,  # 添加通信状态
                    }
                )

                # 检查错误状态并采取相应处理
                if status.error_state == ErrorState.MOTOR_DISABLED:
                    # 如果是电机失能，发送使能指令
                    logger.info("检测到电机失能，发送使能指令")
                    motor.enable()
                elif status.error_state != ErrorState.MOTOR_ENABLED:
                    # 其他错误状态（过压、欠压、过流等），发送清除错误指令
                    logger.error(f"电机错误: {status.error_description}")
                    logger.info("发送清除错误指令")
                    motor.clear_error()
            else:
                # 没有收到电机状态更新，但仍需要更新通信状态
                motor_data.update({
                    "communication_lost": communication_lost,
                    "control_mode": current_mode,
                    "emergency_stop": emergency_gpio.is_emergency_stop_active(),
                })

            # 处理配置更新 - 直接使用最新配置
            with config_lock:
                if latest_config_update is not None:
                    if validate_config(latest_config_update):
                        save_config(latest_config_update)
                        # 重新加载配置到缓存
                        config = load_config()
                        logger.info("配置已更新并重新加载到缓存")
                    latest_config_update = None  # 处理完后清空

        except Exception as e:
            logger.error(f"电机控制任务错误: {e}")

        time.sleep(0.1)  # 10Hz控制频率，降低CPU占用

    # 电机控制任务退出时发送失能指令
    logger.info("电机控制任务正在停止，发送失能指令...")
    try:
        motor.set_velocity_mode(0.0)  # 先停止电机
        time.sleep(0.1)  # 等待速度指令生效
        if motor.disable():
            logger.info("电机控制任务：电机失能指令发送成功")
        else:
            logger.warning("电机控制任务：电机失能指令发送失败")
    except Exception as e:
        logger.error(f"电机控制任务：发送电机失能指令时出错: {e}")

    logger.info("电机控制任务已停止。")  # 任务退出日志


# 实时广播任务 - 高频率直接广播最新数据
async def realtime_broadcast_task():
    """实时广播任务，直接发送最新数据，无缓存延迟"""
    global running_flag
    logger.info("实时广播任务开始运行。")

    broadcast_count = 0
    sbus_broadcast_count = 0
    while running_flag:
        try:
            # 直接广播最新的motor_data，无需任何缓存
            if manager.active_connections:
                await manager.broadcast({"type": "motor_status", "data": motor_data})
                broadcast_count += 1

                # 每5次电机状态广播，广播一次SBUS数据和连接状态（10Hz频率）
                if sbus_broadcast_count % 2 == 0:
                    # 使用新的mavlink处理类获取SBUS数据
                    sbus_data = remote_control_handler.get_sbus_data()
                    await manager.broadcast({"type": "sbus_data", "data": sbus_data})

                    # 广播mavlink连接状态
                    mavlink_status = mavlink_handler.get_connection_status()
                    await manager.broadcast(
                        {"type": "mavlink_status", "data": mavlink_status}
                    )

                sbus_broadcast_count += 1

        except Exception as e:
            logger.error(f"实时广播任务出错: {e}")

        # 50Hz更新频率，平衡实时性和性能
        await asyncio.sleep(0.02)

    logger.info("实时广播任务已停止。")


@asynccontextmanager
async def lifespan(_app):
    """FastAPI 应用的生命周期事件处理"""
    global running_flag, motor_control_thread, motor_data
    global realtime_broadcast_task_ref, pwm_controller
    logger.info("应用启动中...")

    # 初始化急停GPIO
    if not emergency_gpio.setup():
        logger.error("急停GPIO初始化失败，程序将继续运行但无急停功能")
    else:
        logger.info("急停GPIO初始化成功")

    # 确保配置文件存在
    default_config = get_default_config()
    if not Path("config.toml").exists():
        logger.info("配置文件不存在，创建默认配置")
        save_config(default_config)

    # 加载并打印配置信息
    config = load_config()

    # 初始化PWM控制器
    pwm1_pin = config.get("pwm1_pin", 21)
    pwm2_pin = config.get("pwm2_pin", 22)
    pwm_duty_limit = config.get("pwm_duty_limit", 0.6)

    pwm_controller = PWMController(pwm1_pin, pwm2_pin, pwm_duty_limit)
    if not pwm_controller.setup():
        logger.error("PWM控制器初始化失败，程序将继续运行但无PWM输出功能")
    else:
        logger.info("PWM控制器初始化成功")

    # 设置遥控器控制处理器的PWM控制器引用
    remote_control_handler.set_pwm_controller(pwm_controller)

    # 初始化电机
    if not motor.begin():
        logger.error("电机初始化失败!")
        running_flag = False  # 设置标志，防止相关任务启动
        yield  # 继续到应用启动阶段，但无电机控制
        logger.info("应用关闭完成。")
        return

    # 根据用户要求，上电后始终保持电机使能并设置为速度模式
    if not motor.enable():
        logger.error("电机使能失败!")
        running_flag = False  # 设置标志，防止相关任务启动
        yield  # 继续到应用启动阶段，但无电机控制
        logger.info("应用关闭完成。")
        return
    motor_data["is_enabled"] = True  # 设置初始使能状态为 True
    pc_control_handler.disable_motor()  # 初始化web界面使能状态为失能
    logger.info("电机初始化完成并使能。")

    # 启动急停监控任务 (线程)
    if emergency_gpio.is_initialized():
        emergency_gpio.start_monitor()

    # 启动电机控制任务 (线程)
    motor_control_thread = threading.Thread(target=motor_control_task, daemon=True)
    motor_control_thread.start()
    logger.info("电机控制任务已启动。")

    # 启动实时广播任务
    realtime_broadcast_task_ref = asyncio.create_task(realtime_broadcast_task())
    logger.info("实时广播任务已启动，无缓存延迟。")

    yield  # 应用启动完成，现在可以处理请求

    # 应用关闭阶段
    logger.info("应用关闭中...")
    running_flag = False  # 设置退出标志，通知后台任务停止

    # 等待后台任务完成
    emergency_gpio.stop_monitor()

    # 停止PWM输出
    if pwm_controller and pwm_controller.is_initialized():
        pwm_controller.stop_pwm()
        logger.info("PWM输出已停止")

    if motor_control_thread and motor_control_thread.is_alive():
        logger.info("等待电机控制任务完成...")
        motor_control_thread.join(timeout=5)  # 最多等待5秒
        if motor_control_thread.is_alive():
            logger.warning("电机控制任务未能在指定时间内退出！")

    # 停止实时广播任务
    if realtime_broadcast_task_ref and not realtime_broadcast_task_ref.done():
        logger.info("等待实时广播任务完成...")
        try:
            await asyncio.wait_for(realtime_broadcast_task_ref, timeout=5)
        except asyncio.TimeoutError:
            logger.warning("实时广播任务未能在指定时间内退出！")

    # 程序退出时向电机发送失能指令（如果套接字仍然有效）
    if motor.socket:
        logger.info("正在向电机发送失能指令..")
        try:
            motor.set_velocity_mode(0.0)  # 先停止电机
            time.sleep(0.1)  # 等待速度指令生效
            if motor.disable():
                logger.info("电机失能指令发送成功")
            else:
                logger.warning("电机失能指令发送失败")
        except Exception as e:
            logger.error(f"发送电机失能指令时出错: {e}")

        # 关闭CAN套接字
        try:
            motor.shutdown()
            logger.info("CAN套接字已关闭。")
        except Exception as e:
            logger.error(f"关闭CAN套接字时出错: {e}")
    else:
        logger.info("CAN套接字已经关闭，跳过失能指令发送。")

    logger.info("应用关闭完成。")


# 全局状态访问函数
def get_motor_data():
    return motor_data


def get_current_mode():
    return current_mode


def set_current_mode(mode):
    global current_mode
    current_mode = mode


def get_latest_config_update():
    return latest_config_update


def set_latest_config_update(config):
    global latest_config_update
    latest_config_update = config


# 创建FastAPI应用和相关组件
app, manager, message_handler = create_app(
    pc_control_handler,
    motor,
    config_lock,
    lifespan,
    get_motor_data,
    get_current_mode,
    set_current_mode,
    get_latest_config_update,
    set_latest_config_update,
)

if __name__ == "__main__":
    # 确保配置文件存在
    default_config = get_default_config()
    if not Path("config.toml").exists():
        save_config(default_config)

    try:
        # 启动服务器
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            lifespan="on",  # 明确启用 lifespan
        )
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，程序正在退出...")
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}")
    finally:
        # 程序退出处理现在由FastAPI的lifespan管理
        logger.info("__main__: 程序退出，清理工作由FastAPI lifespan处理")

# 测试增量编译 - 版本 1.0.3
