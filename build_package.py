#!/usr/bin/env python3
"""
编译打包脚本 - 将Python工程编译成.so文件并打包 (支持增量编译)
使用方法:
  sudo python build_package.py          # 增量编译模式
  sudo python build_package.py --clean  # 清理缓存，强制完整重新编译
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import tempfile
import multiprocessing

# 配置
SOURCE_DIR = Path(__file__).parent  # 当前工程目录
TARGET_DIR = Path("/opt/OPT-DM-WINCH-LINUX")  # 目标目录
INSTALL_DIR = SOURCE_DIR / "install" / "OPT-DM-WINCH-LINUX"  # 工程install目录
BUILD_DIR = Path(tempfile.mkdtemp())  # 临时构建目录

def check_dependencies():
    """检查必要的依赖"""
    try:
        import Cython
        print(f"✓ Cython版本: {Cython.__version__}")
    except ImportError:
        print("❌ 缺少Cython，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "Cython"], check=True)
        print("✓ Cython安装完成")

def create_setup_py(changed_files=None):
    """创建setup.py文件用于编译（支持增量编译）"""

    # 如果没有指定变化的文件，编译所有文件
    if changed_files is None:
        changed_files = []

    setup_content = f'''
from setuptools import setup, Extension
from Cython.Build import cythonize
import glob
import os
import json

# 读取变化的文件列表
changed_files = {changed_files}

# 获取所有Python文件
py_files = []
for root, dirs, files in os.walk("lib"):
    for file in files:
        if file.endswith(".py") and file != "__init__.py":
            py_files.append(os.path.join(root, file))

# 添加main.py
py_files.append("main.py")

# 如果有指定变化的文件，优先编译这些文件
if changed_files:
    print("📝 增量编译模式：只编译变化的文件")
    # 将相对路径转换为与py_files匹配的格式
    changed_py_files = []
    for changed_file in changed_files:
        if changed_file in py_files:
            changed_py_files.append(changed_file)

    if changed_py_files:
        print(f"  将编译: {{', '.join(changed_py_files)}}")
        py_files = changed_py_files
    else:
        print("  没有找到需要编译的Python文件")
else:
    print("🔨 完整编译模式：编译所有文件")

# 创建扩展模块
extensions = []
for py_file in py_files:
    module_name = py_file.replace("/", ".").replace("\\\\", ".").replace(".py", "")
    extensions.append(Extension(
        module_name,
        [py_file],
        # 添加必要的编译选项，启用优化
        extra_compile_args=['-O3', '-ffast-math'],
        extra_link_args=[]
    ))

setup(
    ext_modules=cythonize(
        extensions,
        compiler_directives={{
            'language_level': 3,
            'embedsignature': True,
            'boundscheck': False,
            'wraparound': False,
            'cdivision': True,  # 启用C风格除法优化
            'initializedcheck': False,  # 禁用初始化检查以提高性能
        }},
        # 启用增量编译缓存
        cache=True,
        # 并行编译 - 动态获取CPU核心数
        nthreads=max(1, __import__('multiprocessing').cpu_count() - 2)
    ),
    zip_safe=False,
)
'''

    setup_path = BUILD_DIR / "setup.py"
    with open(setup_path, 'w', encoding='utf-8') as f:
        f.write(setup_content)

    return setup_path

def copy_source_files():
    """复制源文件到构建目录"""
    print("📁 复制源文件...")
    
    # 复制main.py
    shutil.copy2(SOURCE_DIR / "main.py", BUILD_DIR / "main.py")
    
    # 复制lib目录
    shutil.copytree(SOURCE_DIR / "lib", BUILD_DIR / "lib")
    
    # 复制web静态文件
    web_static_src = SOURCE_DIR / "lib" / "web" / "static"
    web_static_dst = BUILD_DIR / "lib" / "web" / "static"
    if web_static_src.exists():
        shutil.copytree(web_static_src, web_static_dst, dirs_exist_ok=True)
    
    print("✓ 源文件复制完成")

def check_file_changes():
    """检查文件是否有变化，用于增量编译"""
    cache_file = SOURCE_DIR / ".build_cache"
    current_hashes = {}

    # 计算当前文件的哈希值
    import hashlib
    import json

    print("  🔍 检查文件变化...")

    # 检查main.py
    if (SOURCE_DIR / "main.py").exists():
        with open(SOURCE_DIR / "main.py", 'rb') as f:
            current_hashes["main.py"] = hashlib.md5(f.read()).hexdigest()

    # 检查lib目录下的所有Python文件
    lib_dir = SOURCE_DIR / "lib"
    if lib_dir.exists():
        for py_file in lib_dir.rglob("*.py"):
            if py_file.name != "__init__.py":  # 跳过__init__.py文件
                relative_path = py_file.relative_to(SOURCE_DIR)
                try:
                    with open(py_file, 'rb') as f:
                        current_hashes[str(relative_path)] = hashlib.md5(f.read()).hexdigest()
                except Exception as e:
                    print(f"    ⚠️  无法读取文件 {relative_path}: {e}")

    # 读取缓存的哈希值
    cached_hashes = {}
    if cache_file.exists():
        try:
            with open(cache_file, 'r') as f:
                cached_hashes = json.load(f)
        except Exception as e:
            print(f"    ⚠️  无法读取缓存文件: {e}")
            cached_hashes = {}

    # 比较哈希值
    changed_files = []
    new_files = []

    for file_path, current_hash in current_hashes.items():
        if file_path not in cached_hashes:
            new_files.append(file_path)
            changed_files.append(file_path)
        elif cached_hashes[file_path] != current_hash:
            changed_files.append(file_path)

    # 检查删除的文件
    deleted_files = []
    for file_path in cached_hashes:
        if file_path not in current_hashes:
            deleted_files.append(file_path)

    # 保存当前哈希值
    try:
        with open(cache_file, 'w') as f:
            json.dump(current_hashes, f, indent=2)
    except Exception as e:
        print(f"    ⚠️  无法保存缓存文件: {e}")

    # 输出详细信息
    if new_files:
        print(f"    ➕ 新增文件: {len(new_files)} 个")
    if deleted_files:
        print(f"    ➖ 删除文件: {len(deleted_files)} 个")
    if changed_files and not new_files:
        print(f"    📝 修改文件: {len(changed_files)} 个")

    return changed_files

def copy_existing_so_files(source_dir):
    """从已存在的目录复制.so文件到构建目录"""
    try:
        for so_file in source_dir.glob("**/*.so"):
            relative_path = so_file.relative_to(source_dir)
            target_path = BUILD_DIR / relative_path
            target_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(so_file, target_path)
            print(f"    ✓ 复制 {relative_path}")
        return True
    except Exception as e:
        print(f"    ❌ 复制.so文件失败: {e}")
        return False

def copy_existing_compiled_files():
    """复制已存在的编译文件（用于增量编译）"""
    try:
        # 优先从目标目录复制
        if TARGET_DIR.exists():
            target_so_files = list(TARGET_DIR.glob("**/*.so"))
            if target_so_files:
                print("    从目标目录复制已编译文件...")
                return copy_existing_so_files(TARGET_DIR)

        # 其次从install目录复制
        if INSTALL_DIR.exists():
            install_so_files = list(INSTALL_DIR.glob("**/*.so"))
            if install_so_files:
                print("    从install目录复制已编译文件...")
                return copy_existing_so_files(INSTALL_DIR)

        print("    没有找到已编译的.so文件，将进行完整编译")
        return True

    except Exception as e:
        print(f"    ❌ 复制已编译文件失败: {e}")
        return False

def compile_to_so(changed_files):
    """编译Python文件为.so文件（支持增量编译）"""
    print("🔨 开始编译...")

    if changed_files:
        print(f"  检测到 {len(changed_files)} 个文件有变化:")
        for file in changed_files[:5]:  # 只显示前5个
            print(f"    - {file}")
        if len(changed_files) > 5:
            print(f"    ... 还有 {len(changed_files) - 5} 个文件")
        print("  将进行增量编译")

        # 增量编译时，先复制已存在的.so文件，再编译变化的文件
        print("  复制已编译的.so文件...")
        copy_existing_compiled_files()

    else:
        # 检查目标目录是否存在已编译的.so文件
        target_so_files = []
        if TARGET_DIR.exists():
            target_so_files = list(TARGET_DIR.glob("**/*.so"))

        # 检查install目录是否存在已编译的.so文件
        install_so_files = []
        if INSTALL_DIR.exists():
            install_so_files = list(INSTALL_DIR.glob("**/*.so"))

        if not target_so_files and not install_so_files:
            print("  没有找到已编译的.so文件，将进行完整编译")
        else:
            print(f"  没有检测到文件变化，且已存在 {len(target_so_files + install_so_files)} 个.so文件")
            print("  跳过编译，直接进行打包")

            # 从目标目录或install目录复制.so文件到构建目录
            if target_so_files:
                print("  从目标目录复制已编译文件...")
                copy_existing_so_files(TARGET_DIR)
            elif install_so_files:
                print("  从install目录复制已编译文件...")
                copy_existing_so_files(INSTALL_DIR)

            return True

    # 切换到构建目录
    original_cwd = os.getcwd()
    os.chdir(BUILD_DIR)

    try:
        # 获取CPU核心数用于并行编译
        cpu_count = multiprocessing.cpu_count()
        # 使用 cpu_count - 2 个核心，为系统和其他进程留出资源
        parallel_jobs = max(1, cpu_count - 2)
        print(f"  检测到 {cpu_count} 个CPU核心，使用 {parallel_jobs} 个核心进行并行编译")

        # 构建编译参数
        compile_args = [
            sys.executable, "setup.py", "build_ext", "--inplace",
            f"-j{parallel_jobs}"
        ]

        # 不使用--force参数，让Cython自己判断哪些文件需要重新编译
        print("  启用智能增量编译（Cython自动检测变化）")

        print(f"  执行: {' '.join(compile_args)}")
        process = subprocess.Popen(
            compile_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # 实时显示输出
        for line in process.stdout:
            print(f"    {line.rstrip()}")

        process.wait()

        if process.returncode != 0:
            print(f"❌ 编译失败，返回码: {process.returncode}")
            return False

        print("✓ 编译完成")
        return True

    except Exception as e:
        print(f"❌ 编译过程出错: {e}")
        return False

    finally:
        os.chdir(original_cwd)

def create_init_files():
    """创建必要的__init__.py文件"""
    print("📝 创建__init__.py文件...")

    # 在构建目录中创建__init__.py文件
    init_dirs = [
        BUILD_DIR / "lib",
        BUILD_DIR / "lib" / "control",
        BUILD_DIR / "lib" / "hardware",
        BUILD_DIR / "lib" / "utils",
        BUILD_DIR / "lib" / "web"
    ]

    for init_dir in init_dirs:
        if init_dir.exists():
            init_file = init_dir / "__init__.py"
            if not init_file.exists():
                init_file.touch()

    print("✓ __init__.py文件创建完成")

def create_run_script():
    """创建精简的run.py入口脚本"""
    run_content = '''#!/usr/bin/env python3
"""
OPT-DM-WINCH-LINUX 运行脚本
编译版本入口文件
"""

import sys
import os
import uvicorn
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        # 导入编译后的主模块和必要的函数
        from lib.utils.config_manager import get_default_config, save_config

        # 确保配置文件存在
        default_config = get_default_config()
        if not Path("config.toml").exists():
            save_config(default_config)

        # 导入主模块以获取app对象
        import main

        logger.info("🚀 启动 OPT-DM-WINCH-LINUX 编译版本")

        # 启动服务器
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            lifespan="on",  # 明确启用 lifespan
        )

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，程序正在退出...")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有编译文件完整")
        print("当前目录内容:")
        for item in current_dir.iterdir():
            print(f"  {item.name}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 程序退出处理现在由FastAPI的lifespan管理
        logger.info("run.py: 程序退出，清理工作由FastAPI lifespan处理")

if __name__ == "__main__":
    main()
'''

    run_path = BUILD_DIR / "run.py"
    with open(run_path, 'w', encoding='utf-8') as f:
        f.write(run_content)

    # 设置执行权限
    os.chmod(run_path, 0o777)
    print("✓ run.py创建完成")

def create_start_script():
    """创建启动脚本"""
    start_content = '''#!/bin/bash
# OPT-DM-WINCH-LINUX 启动脚本

echo "🚀 启动 OPT-DM-WINCH-LINUX..."
echo "📁 当前目录: $(pwd)"

# 检查Python版本
python_version=$(python3 --version 2>&1)
echo "Python版本: $python_version"

# 检查依赖
if [ -f "requirements.txt" ]; then
    echo "📦 检查依赖..."
    pip3 install -r requirements.txt --quiet
fi

# 运行程序
echo "▶️  启动程序..."
python3 run.py
'''

    start_path = TARGET_DIR / "start.sh"
    with open(start_path, 'w', encoding='utf-8') as f:
        f.write(start_content)

    # 设置执行权限
    os.chmod(start_path, 0o777)
    print("  ✓ start.sh")

def set_permissions_recursive(directory, mode=0o777):
    """递归设置目录下所有文件和文件夹的权限"""
    print(f"🔐 设置权限 {oct(mode)} 到 {directory}")

    try:
        # 设置目录本身的权限
        os.chmod(directory, mode)

        # 递归设置所有子文件和子目录的权限
        for root, dirs, files in os.walk(directory):
            # 设置目录权限
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                os.chmod(dir_path, mode)

            # 设置文件权限
            for file_name in files:
                file_path = os.path.join(root, file_name)
                os.chmod(file_path, mode)

        print(f"  ✓ 权限设置完成")
        return True

    except Exception as e:
        print(f"  ❌ 权限设置失败: {e}")
        return False

def package_files():
    """打包文件到目标目录"""
    print("📦 打包文件...")

    # 创建目标目录
    if TARGET_DIR.exists():
        print(f"🗑️  删除现有目录: {TARGET_DIR}")
        shutil.rmtree(TARGET_DIR)

    # 创建目标目录，需要sudo权限
    try:
        TARGET_DIR.mkdir(parents=True, exist_ok=True)
    except PermissionError:
        print(f"❌ 权限不足，无法创建 {TARGET_DIR}")
        print("请使用 sudo 运行此脚本，或者手动创建目录并设置权限")
        return False

    # 复制.so文件
    for so_file in BUILD_DIR.glob("**/*.so"):
        relative_path = so_file.relative_to(BUILD_DIR)
        target_path = TARGET_DIR / relative_path
        target_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(so_file, target_path)
        print(f"  ✓ {relative_path}")

    # 复制__init__.py文件
    for init_file in BUILD_DIR.glob("**/__init__.py"):
        relative_path = init_file.relative_to(BUILD_DIR)
        target_path = TARGET_DIR / relative_path
        target_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(init_file, target_path)
        print(f"  ✓ {relative_path}")

    # 复制run.py
    shutil.copy2(BUILD_DIR / "run.py", TARGET_DIR / "run.py")
    print("  ✓ run.py")

    # 复制config.toml
    config_src = SOURCE_DIR / "config.toml"
    config_dst = TARGET_DIR / "config.toml"
    shutil.copy2(config_src, config_dst)
    print("  ✓ config.toml")

    # 复制requirements.txt（如果存在）
    if (SOURCE_DIR / "requirements.txt").exists():
        shutil.copy2(SOURCE_DIR / "requirements.txt", TARGET_DIR / "requirements.txt")
        print("  ✓ requirements.txt")

    # 复制web静态文件
    web_static_src = BUILD_DIR / "lib" / "web" / "static"
    web_static_dst = TARGET_DIR / "lib" / "web" / "static"
    if web_static_src.exists():
        shutil.copytree(web_static_src, web_static_dst, dirs_exist_ok=True)
        print("  ✓ web静态文件")

    # 创建启动脚本
    create_start_script()

    # 设置所有文件和目录为777权限
    set_permissions_recursive(TARGET_DIR, 0o777)

    print(f"✓ 打包完成: {TARGET_DIR}")
    return True

def copy_to_install_dir():
    """复制到工程install目录"""
    print("📁 复制到install目录...")

    # 创建install目录
    if INSTALL_DIR.exists():
        print(f"🗑️  删除现有install目录: {INSTALL_DIR}")
        shutil.rmtree(INSTALL_DIR)

    INSTALL_DIR.parent.mkdir(parents=True, exist_ok=True)

    # 复制整个目标目录到install目录
    shutil.copytree(TARGET_DIR, INSTALL_DIR)

    # 设置install目录下所有文件和目录为777权限
    set_permissions_recursive(INSTALL_DIR, 0o777)

    print(f"✓ install目录创建完成: {INSTALL_DIR}")

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    try:
        shutil.rmtree(BUILD_DIR)
        print("✓ 清理完成")
    except Exception as e:
        print(f"⚠️  清理临时文件时出现警告: {e}")
        print("  这不会影响编译结果")

def clean_cache():
    """清理编译缓存，强制完整重新编译"""
    cache_file = SOURCE_DIR / ".build_cache"
    if cache_file.exists():
        cache_file.unlink()
        print("🧹 已清理编译缓存")
    else:
        print("ℹ️  没有找到编译缓存")

def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--clean":
        print("🚀 开始编译打包 OPT-DM-WINCH-LINUX (清理缓存模式)")
        clean_cache()
    else:
        print("🚀 开始编译打包 OPT-DM-WINCH-LINUX (支持增量编译)")

    print(f"源目录: {SOURCE_DIR}")
    print(f"目标目录: {TARGET_DIR}")
    print(f"构建目录: {BUILD_DIR}")
    print(f"CPU核心数: {multiprocessing.cpu_count()} (使用 {max(1, multiprocessing.cpu_count() - 2)} 个)")
    print("-" * 50)
    
    try:
        # 1. 检查依赖
        check_dependencies()
        
        # 2. 复制源文件
        copy_source_files()

        # 3. 创建__init__.py文件
        create_init_files()

        # 4. 检查文件变化
        changed_files = check_file_changes()

        # 5. 创建setup.py（传递变化的文件列表）
        create_setup_py(changed_files)

        # 6. 编译为.so文件
        if not compile_to_so(changed_files):
            return False

        # 6. 创建run.py
        create_run_script()

        # 7. 打包文件
        if not package_files():
            return False

        # 8. 复制到install目录
        copy_to_install_dir()

        print("-" * 50)
        print("🎉 编译打包完成!")
        print(f"📁 输出目录: {TARGET_DIR} (权限: 777)")
        print(f"📁 install目录: {INSTALL_DIR} (权限: 777)")
        print(f"🏃 运行命令: cd {TARGET_DIR} && python run.py")
        print("💡 增量编译缓存已保存，下次编译会更快")

        return True
        
    except Exception as e:
        print(f"❌ 编译打包失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
